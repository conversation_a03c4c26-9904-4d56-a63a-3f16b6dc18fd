# BuildBid - Construction Platform Backend

🏗️ **BuildBid** is a comprehensive construction platform similar to Zomato, connecting users with material vendors and skilled workers (raj mistris) for construction needs.

## 🚀 Features

### 👥 Multi-Role Architecture
- **Users**: Order materials, hire workers, track orders
- **Vendors**: Manage inventory, accept orders, track deliveries  
- **Workers**: Manage profile, accept bookings, track earnings
- **Admin**: Manage platform, verify users, analytics

### 🛒 Material Ordering
- Browse materials by category
- Location-based vendor discovery
- Real-time inventory management
- Order tracking with live updates
- Multiple payment options (COD, Online, Wallet)

### 👷 Worker Hiring
- Skill-based worker search
- Rating and review system
- Hourly/daily rate booking
- Work progress tracking
- Portfolio and certification display

### 💳 Payment Integration
- Razorpay payment gateway
- Secure payment verification
- Multiple payment methods
- Automatic refund handling

### 🔔 Real-time Features
- Firebase push notifications
- Socket.IO for live updates
- Order status tracking
- Chat system (planned)

## 🛠️ Tech Stack

- **Backend**: Node.js + Express.js
- **Database**: PostgreSQL with Sequelize ORM
- **Authentication**: JWT with role-based access
- **Payments**: Razorpay integration
- **Real-time**: Socket.IO + Firebase
- **File Storage**: AWS S3 (planned)
- **Logging**: Winston
- **Validation**: Express Validator

## 📦 Installation

### Prerequisites
- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### 1. Clone Repository
```bash
git clone <repository-url>
cd buildbid-backend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Setup
```bash
cp .env.example .env
```

Edit `.env` file with your configuration:
```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=buildbid_db
DB_USER=postgres
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your_super_secret_jwt_key

# Razorpay
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Firebase
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email
```

### 4. Database Setup
```bash
# Create PostgreSQL database
createdb buildbid_db

# Initialize database with tables and seed data
npm run migrate
```

### 5. Start Development Server
```bash
npm run dev
```

Server will start on `http://localhost:5000`

## 📚 API Documentation

### Authentication Endpoints
```
POST /api/auth/register     - User registration
POST /api/auth/login        - User login
GET  /api/auth/me          - Get current user
POST /api/auth/logout      - Logout user
```

### Material Endpoints
```
GET  /api/materials        - Get all materials (with filters)
GET  /api/materials/:id    - Get single material
POST /api/materials        - Create material (Vendor only)
PUT  /api/materials/:id    - Update material (Vendor only)
```

### Order Endpoints
```
POST /api/orders           - Create new order
GET  /api/orders           - Get user's orders
GET  /api/orders/:id       - Get single order
PUT  /api/orders/:id/status - Update order status (Vendor only)
```

### Worker Endpoints
```
GET  /api/workers          - Get all workers (with filters)
GET  /api/workers/:id      - Get single worker
PUT  /api/workers/profile  - Update worker profile
```

### Payment Endpoints
```
POST /api/payments/create-order     - Create Razorpay order
POST /api/payments/verify-order     - Verify payment
POST /api/payments/create-booking-order - Create booking payment
POST /api/payments/verify-booking   - Verify booking payment
```

## 🗂️ Project Structure

```
buildbid-backend/
├── config/
│   └── database.js         # Database configuration
├── middleware/
│   ├── auth.js            # Authentication middleware
│   └── errorHandler.js    # Error handling middleware
├── models/
│   ├── User.js            # User model
│   ├── Vendor.js          # Vendor model
│   ├── Material.js        # Material model
│   ├── Order.js           # Order model
│   ├── Worker.js          # Worker model
│   └── index.js           # Model associations
├── routes/
│   ├── auth.js            # Authentication routes
│   ├── materials.js       # Material routes
│   ├── orders.js          # Order routes
│   ├── workers.js         # Worker routes
│   ├── vendors.js         # Vendor routes
│   ├── admin.js           # Admin routes
│   └── payments.js        # Payment routes
├── scripts/
│   ├── initDatabase.js    # Database initialization
│   └── seedCategories.js  # Seed categories data
├── utils/
│   └── logger.js          # Winston logger setup
├── server.js              # Main server file
└── package.json
```

## 🔐 Default Admin Credentials

After running database initialization:
- **Email**: <EMAIL>
- **Password**: admin123

## 🚀 Deployment

### Using Docker (Recommended)
```bash
# Build Docker image
docker build -t buildbid-backend .

# Run with docker-compose
docker-compose up -d
```

### Manual Deployment
1. Set up PostgreSQL database
2. Configure environment variables
3. Run database migrations
4. Start the application with PM2

```bash
npm install -g pm2
pm2 start server.js --name buildbid-api
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## 📊 Database Schema

### Key Tables
- **users**: User accounts (all roles)
- **vendors**: Vendor-specific data
- **workers**: Worker-specific data  
- **categories**: Material categories
- **materials**: Product catalog
- **orders**: Material orders
- **order_items**: Order line items
- **worker_bookings**: Worker hire bookings

## 🔔 Notifications

The platform uses Firebase Cloud Messaging for push notifications:
- Order status updates
- New booking notifications
- Payment confirmations
- Promotional messages

## 🛡️ Security Features

- JWT-based authentication
- Role-based access control
- Request rate limiting
- Input validation and sanitization
- SQL injection prevention
- XSS protection with Helmet.js

## 📈 Monitoring & Logging

- Winston logger for structured logging
- Error tracking and reporting
- Performance monitoring
- Database query logging

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and queries:
- Email: <EMAIL>
- Documentation: [API Docs](https://api.buildbid.com/docs)
- Issues: [GitHub Issues](https://github.com/buildbid/backend/issues)

---

**Built with ❤️ for the construction industry**
