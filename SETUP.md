# 🚀 BuildBid Setup Guide

## Quick Start (Without Database)

If you want to test the API structure without setting up PostgreSQL:

```bash
# Install dependencies (already done)
npm install

# Start server in test mode
npm run test-server
```

## Full Setup (With PostgreSQL)

### 1. Install PostgreSQL

**macOS (using Homebrew):**
```bash
brew install postgresql
brew services start postgresql
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

**Windows:**
Download and install from: https://www.postgresql.org/download/windows/

### 2. Create Database

```bash
# Connect to PostgreSQL
psql postgres

# Create database and user
CREATE DATABASE buildbid_db;
CREATE USER buildbid_user WITH PASSWORD 'buildbid123';
GRANT ALL PRIVILEGES ON DATABASE buildbid_db TO buildbid_user;
\q
```

### 3. Update Environment Variables

Edit `.env` file:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=buildbid_db
DB_USER=buildbid_user
DB_PASSWORD=buildbid123
```

### 4. Initialize Database

```bash
# Run database initialization
npm run migrate
```

### 5. Start Server

```bash
# Development mode
npm run dev

# Production mode
npm start
```

## 🧪 Testing the API

Once the server is running, you can test the endpoints:

### Health Check
```bash
curl http://localhost:5000/health
```

### Register User
```bash
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "phone": "**********",
    "password": "password123",
    "role": "user"
  }'
```

### Login
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

## 📱 Frontend Integration

The backend is ready to integrate with:
- **React Native** mobile app
- **React.js** web dashboard
- **Flutter** mobile app (alternative)

### API Base URL
- Development: `http://localhost:5000/api`
- Production: `https://your-domain.com/api`

### Authentication
All protected routes require JWT token in header:
```
Authorization: Bearer <your-jwt-token>
```

## 🔧 Configuration

### Required Environment Variables
```env
# Database (Required)
DB_HOST=localhost
DB_NAME=buildbid_db
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# JWT (Required)
JWT_SECRET=your_secret_key

# Razorpay (Required for payments)
RAZORPAY_KEY_ID=your_key_id
RAZORPAY_KEY_SECRET=your_key_secret
```

### Optional Environment Variables
```env
# Firebase (for notifications)
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email

# AWS S3 (for file uploads)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_S3_BUCKET=your_bucket_name
```

## 🚀 Deployment Options

### 1. Railway (Recommended)
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

### 2. Heroku
```bash
# Install Heroku CLI
# Create Heroku app
heroku create buildbid-api

# Add PostgreSQL addon
heroku addons:create heroku-postgresql:hobby-dev

# Deploy
git push heroku main
```

### 3. DigitalOcean App Platform
- Connect your GitHub repository
- Set environment variables
- Deploy automatically

## 📊 Database Schema

The platform includes these main entities:
- **Users** (multi-role: user, vendor, worker, admin)
- **Vendors** (business profiles)
- **Workers** (skilled worker profiles)
- **Categories** (material categories)
- **Materials** (product catalog)
- **Orders** (material orders)
- **WorkerBookings** (worker hire bookings)

## 🔐 Default Admin Access

After database initialization:
- **Email**: <EMAIL>
- **Password**: admin123

## 🆘 Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
brew services list | grep postgresql  # macOS
sudo systemctl status postgresql      # Linux

# Test connection
psql -h localhost -U buildbid_user -d buildbid_db
```

### Port Already in Use
```bash
# Find process using port 5000
lsof -i :5000

# Kill process
kill -9 <PID>
```

### Module Not Found Errors
```bash
# Clear npm cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

## 📞 Support

If you encounter any issues:
1. Check the logs in `logs/` directory
2. Verify environment variables
3. Ensure PostgreSQL is running
4. Check firewall settings

---

**Happy Building! 🏗️**
