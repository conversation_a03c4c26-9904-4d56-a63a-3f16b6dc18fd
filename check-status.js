#!/usr/bin/env node

const axios = require('axios');
const chalk = require('chalk');

const BASE_URL = 'http://localhost:5000';

console.log(chalk.blue.bold('\n🏗️  BuildBid Platform Status Check\n'));

async function checkEndpoint(name, endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      timeout: 5000,
    };
    
    if (data) {
      config.data = data;
      config.headers = { 'Content-Type': 'application/json' };
    }
    
    const response = await axios(config);
    console.log(chalk.green(`✅ ${name}: ${response.status} ${response.statusText}`));
    
    if (response.data) {
      if (response.data.success !== undefined) {
        console.log(chalk.gray(`   Success: ${response.data.success}`));
      }
      if (response.data.message) {
        console.log(chalk.gray(`   Message: ${response.data.message}`));
      }
      if (response.data.data && typeof response.data.data === 'object') {
        const keys = Object.keys(response.data.data);
        if (keys.length > 0) {
          console.log(chalk.gray(`   Data keys: ${keys.join(', ')}`));
        }
      }
    }
    
    return true;
  } catch (error) {
    if (error.response) {
      console.log(chalk.red(`❌ ${name}: ${error.response.status} ${error.response.statusText}`));
      if (error.response.data?.message) {
        console.log(chalk.gray(`   Error: ${error.response.data.message}`));
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.log(chalk.red(`❌ ${name}: Connection refused - Server not running`));
    } else {
      console.log(chalk.red(`❌ ${name}: ${error.message}`));
    }
    return false;
  }
}

async function runStatusCheck() {
  console.log(chalk.yellow('Checking BuildBid API endpoints...\n'));
  
  const checks = [
    ['Health Check', '/health'],
    ['API Test', '/api/test'],
    ['Categories', '/api/categories'],
    ['Materials', '/api/materials'],
    ['Workers', '/api/workers'],
  ];
  
  let passedChecks = 0;
  
  for (const [name, endpoint] of checks) {
    const success = await checkEndpoint(name, endpoint);
    if (success) passedChecks++;
    console.log(''); // Empty line for spacing
  }
  
  // Test authentication endpoints
  console.log(chalk.yellow('Testing authentication endpoints...\n'));
  
  const testUser = {
    name: 'Test User',
    email: `test${Date.now()}@buildbid.com`,
    phone: '**********',
    password: 'password123',
    role: 'user'
  };
  
  const registerSuccess = await checkEndpoint(
    'User Registration', 
    '/api/auth/register', 
    'POST', 
    testUser
  );
  
  if (registerSuccess) passedChecks++;
  console.log('');
  
  const loginSuccess = await checkEndpoint(
    'User Login', 
    '/api/auth/login', 
    'POST', 
    { email: testUser.email, password: testUser.password }
  );
  
  if (loginSuccess) passedChecks++;
  console.log('');
  
  // Summary
  const totalChecks = checks.length + 2; // +2 for auth endpoints
  console.log(chalk.blue.bold('📊 Status Summary:'));
  console.log(chalk.green(`✅ Passed: ${passedChecks}/${totalChecks}`));
  console.log(chalk.red(`❌ Failed: ${totalChecks - passedChecks}/${totalChecks}`));
  
  if (passedChecks === totalChecks) {
    console.log(chalk.green.bold('\n🎉 All systems operational! BuildBid is ready to go!'));
  } else {
    console.log(chalk.yellow.bold('\n⚠️  Some endpoints are not responding. Check server status.'));
  }
  
  console.log(chalk.blue('\n🌐 Access points:'));
  console.log(chalk.cyan(`   API: ${BASE_URL}`));
  console.log(chalk.cyan(`   Demo: file://${process.cwd()}/demo.html`));
  console.log(chalk.cyan(`   Health: ${BASE_URL}/health`));
  
  console.log(chalk.blue('\n📱 Next steps:'));
  console.log(chalk.white('   1. Open demo.html in your browser'));
  console.log(chalk.white('   2. Test API endpoints interactively'));
  console.log(chalk.white('   3. Deploy to production when ready'));
  
  console.log(chalk.green.bold('\n🚀 BuildBid Platform - Ready for Launch!\n'));
}

// Run the status check
runStatusCheck().catch(console.error);
