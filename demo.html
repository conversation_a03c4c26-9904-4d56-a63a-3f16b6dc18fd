<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BuildBid - Live Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35, #E55A2B);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .status {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .status-icon {
            width: 24px;
            height: 24px;
            background: #4CAF50;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .api-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .api-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .api-card:hover {
            transform: translateY(-5px);
        }
        
        .api-card h3 {
            color: #FF6B35;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .api-endpoint {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #FF6B35, #E55A2B);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
        }
        
        .response {
            margin-top: 15px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }
        
        .features {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .features h2 {
            color: #FF6B35;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .feature-item {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ BuildBid Platform</h1>
            <p>निर्माण का भरोसेमंद साथी - Live Demo</p>
        </div>
        
        <div class="status">
            <h2 style="color: #FF6B35; margin-bottom: 20px;">🚀 Platform Status</h2>
            <div class="status-item">
                <div class="status-icon">✓</div>
                <div>
                    <strong>Backend API:</strong> Running on http://localhost:5000
                </div>
            </div>
            <div class="status-item">
                <div class="status-icon">✓</div>
                <div>
                    <strong>Database:</strong> PostgreSQL Ready (Test Mode Active)
                </div>
            </div>
            <div class="status-item">
                <div class="status-icon">✓</div>
                <div>
                    <strong>Authentication:</strong> JWT-based Multi-role System
                </div>
            </div>
            <div class="status-item">
                <div class="status-icon">✓</div>
                <div>
                    <strong>Payment Gateway:</strong> Razorpay Integration Ready
                </div>
            </div>
        </div>
        
        <div class="api-demo">
            <div class="api-card">
                <h3>🏥 Health Check</h3>
                <div class="api-endpoint">GET /health</div>
                <button class="test-btn" onclick="testAPI('/health', 'health-response')">Test Health Check</button>
                <div id="health-response" class="response"></div>
            </div>
            
            <div class="api-card">
                <h3>📋 Categories</h3>
                <div class="api-endpoint">GET /api/categories</div>
                <button class="test-btn" onclick="testAPI('/api/categories', 'categories-response')">Get Categories</button>
                <div id="categories-response" class="response"></div>
            </div>
            
            <div class="api-card">
                <h3>🧱 Materials</h3>
                <div class="api-endpoint">GET /api/materials</div>
                <button class="test-btn" onclick="testAPI('/api/materials', 'materials-response')">Get Materials</button>
                <div id="materials-response" class="response"></div>
            </div>
            
            <div class="api-card">
                <h3>👷 Workers</h3>
                <div class="api-endpoint">GET /api/workers</div>
                <button class="test-btn" onclick="testAPI('/api/workers', 'workers-response')">Get Workers</button>
                <div id="workers-response" class="response"></div>
            </div>
            
            <div class="api-card">
                <h3>🔐 Register User</h3>
                <div class="api-endpoint">POST /api/auth/register</div>
                <button class="test-btn" onclick="testRegister()">Test Registration</button>
                <div id="register-response" class="response"></div>
            </div>
            
            <div class="api-card">
                <h3>🔑 Login</h3>
                <div class="api-endpoint">POST /api/auth/login</div>
                <button class="test-btn" onclick="testLogin()">Test Login</button>
                <div id="login-response" class="response"></div>
            </div>
        </div>
        
        <div class="features">
            <h2>🔥 Platform Features</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <div class="feature-icon">🏗️</div>
                    <h4>Material Ordering</h4>
                    <p>Zomato-like experience for construction materials</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">👷</div>
                    <h4>Worker Hiring</h4>
                    <p>Book skilled raj mistris, plumbers, painters</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💳</div>
                    <h4>Secure Payments</h4>
                    <p>Razorpay integration with multiple payment options</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <h4>Mobile Apps</h4>
                    <p>React Native apps for iOS & Android</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💻</div>
                    <h4>Web Dashboard</h4>
                    <p>Vendor & admin panels with analytics</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔔</div>
                    <h4>Real-time Updates</h4>
                    <p>Live notifications and order tracking</p>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🚀 BuildBid Platform - Production Ready & Scalable</p>
            <p>Built with Node.js, React Native, PostgreSQL, and ❤️</p>
        </div>
    </div>
    
    <script>
        async function testAPI(endpoint, responseId) {
            const responseDiv = document.getElementById(responseId);
            responseDiv.style.display = 'block';
            responseDiv.innerHTML = 'Loading...';
            
            try {
                const response = await fetch(`http://localhost:5000${endpoint}`);
                const data = await response.json();
                responseDiv.innerHTML = JSON.stringify(data, null, 2);
                responseDiv.style.background = '#e8f5e8';
                responseDiv.style.borderLeftColor = '#4CAF50';
            } catch (error) {
                responseDiv.innerHTML = `Error: ${error.message}`;
                responseDiv.style.background = '#ffeaea';
                responseDiv.style.borderLeftColor = '#f44336';
            }
        }
        
        async function testRegister() {
            const responseDiv = document.getElementById('register-response');
            responseDiv.style.display = 'block';
            responseDiv.innerHTML = 'Loading...';
            
            const userData = {
                name: "Demo User",
                email: `demo${Date.now()}@buildbid.com`,
                phone: "9876543210",
                password: "password123",
                role: "user"
            };
            
            try {
                const response = await fetch('http://localhost:5000/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });
                const data = await response.json();
                responseDiv.innerHTML = JSON.stringify(data, null, 2);
                responseDiv.style.background = '#e8f5e8';
                responseDiv.style.borderLeftColor = '#4CAF50';
            } catch (error) {
                responseDiv.innerHTML = `Error: ${error.message}`;
                responseDiv.style.background = '#ffeaea';
                responseDiv.style.borderLeftColor = '#f44336';
            }
        }
        
        async function testLogin() {
            const responseDiv = document.getElementById('login-response');
            responseDiv.style.display = 'block';
            responseDiv.innerHTML = 'Loading...';
            
            const loginData = {
                email: "<EMAIL>",
                password: "password123"
            };
            
            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                const data = await response.json();
                responseDiv.innerHTML = JSON.stringify(data, null, 2);
                responseDiv.style.background = '#e8f5e8';
                responseDiv.style.borderLeftColor = '#4CAF50';
            } catch (error) {
                responseDiv.innerHTML = `Error: ${error.message}`;
                responseDiv.style.background = '#ffeaea';
                responseDiv.style.borderLeftColor = '#f44336';
            }
        }
    </script>
</body>
</html>
