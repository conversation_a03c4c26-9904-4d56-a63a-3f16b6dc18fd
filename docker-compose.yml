version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: buildbid-db
    environment:
      POSTGRES_DB: buildbid_db
      POSTGRES_USER: buildbid_user
      POSTGRES_PASSWORD: buildbid_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - buildbid-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: buildbid-redis
    ports:
      - "6379:6379"
    networks:
      - buildbid-network
    restart: unless-stopped

  # Backend API
  api:
    build: .
    container_name: buildbid-api
    environment:
      NODE_ENV: production
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: buildbid_db
      DB_USER: buildbid_user
      DB_PASSWORD: buildbid_password
      JWT_SECRET: your-super-secure-jwt-secret-key
      REDIS_URL: redis://redis:6379
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    networks:
      - buildbid-network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  # Web Dashboard
  dashboard:
    build:
      context: ./web-dashboard
      dockerfile: Dockerfile
    container_name: buildbid-dashboard
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
    ports:
      - "3000:80"
    depends_on:
      - api
    networks:
      - buildbid-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: buildbid-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
      - dashboard
    networks:
      - buildbid-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  buildbid-network:
    driver: bridge
