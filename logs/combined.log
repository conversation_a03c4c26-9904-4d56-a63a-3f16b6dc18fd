{"level": "error", "message": "Unable to start server:", "name": "SequelizeConnectionRefusedError", "original": {"code": "ECONNREFUSED"}, "parent": {"code": "ECONNREFUSED"}, "service": "buildbid-api", "stack": "SequelizeConnectionRefusedError\n    at Client._connectionCallback (/Users/<USER>/Desktop/oe/GharBanao/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/Users/<USER>/Desktop/oe/GharBanao/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/Users/<USER>/Desktop/oe/GharBanao/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:520:28)\n    at Socket.reportStreamError (/Users/<USER>/Desktop/oe/GharBanao/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:520:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)", "timestamp": "2025-06-11 19:16:55"}