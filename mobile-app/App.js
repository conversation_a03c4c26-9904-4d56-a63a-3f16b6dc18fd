import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { StatusBar, Platform } from 'react-native';
import Toast from 'react-native-toast-message';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/MaterialIcons';
import messaging from '@react-native-firebase/messaging';
import PushNotification from 'react-native-push-notification';

// Screens
import SplashScreen from './src/screens/SplashScreen';
import LoginScreen from './src/screens/LoginScreen';
import RegisterScreen from './src/screens/RegisterScreen';
import HomeScreen from './src/screens/HomeScreen';
import MaterialsScreen from './src/screens/MaterialsScreen';
import MaterialDetailsScreen from './src/screens/MaterialDetailsScreen';
import WorkersScreen from './src/screens/WorkersScreen';
import WorkerDetailsScreen from './src/screens/WorkerDetailsScreen';
import CartScreen from './src/screens/CartScreen';
import OrdersScreen from './src/screens/OrdersScreen';
import OrderDetailsScreen from './src/screens/OrderDetailsScreen';
import ProfileScreen from './src/screens/ProfileScreen';
import BookingScreen from './src/screens/BookingScreen';
import PaymentScreen from './src/screens/PaymentScreen';
import TrackingScreen from './src/screens/TrackingScreen';
import CategoriesScreen from './src/screens/CategoriesScreen';
import SearchScreen from './src/screens/SearchScreen';
import NotificationsScreen from './src/screens/NotificationsScreen';
import SettingsScreen from './src/screens/SettingsScreen';

// Vendor Screens
import VendorDashboardScreen from './src/screens/vendor/VendorDashboardScreen';
import VendorOrdersScreen from './src/screens/vendor/VendorOrdersScreen';
import VendorMaterialsScreen from './src/screens/vendor/VendorMaterialsScreen';
import VendorProfileScreen from './src/screens/vendor/VendorProfileScreen';

// Worker Screens
import WorkerDashboardScreen from './src/screens/worker/WorkerDashboardScreen';
import WorkerBookingsScreen from './src/screens/worker/WorkerBookingsScreen';
import WorkerProfileScreen from './src/screens/worker/WorkerProfileScreen';

import { colors } from './src/config/theme';
import { AuthProvider, useAuth } from './src/context/AuthContext';
import { CartProvider } from './src/context/CartContext';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Configure Push Notifications
PushNotification.configure({
  onRegister: function (token) {
    console.log('TOKEN:', token);
  },
  onNotification: function (notification) {
    console.log('NOTIFICATION:', notification);
  },
  permissions: {
    alert: true,
    badge: true,
    sound: true,
  },
  popInitialNotification: true,
  requestPermissions: Platform.OS === 'ios',
});

// Main Tab Navigator for Users
const UserTabs = () => (
  <Tab.Navigator
    screenOptions={({ route }) => ({
      tabBarIcon: ({ focused, color, size }) => {
        let iconName;
        switch (route.name) {
          case 'Home':
            iconName = 'home';
            break;
          case 'Materials':
            iconName = 'build';
            break;
          case 'Workers':
            iconName = 'person';
            break;
          case 'Orders':
            iconName = 'shopping-cart';
            break;
          case 'Profile':
            iconName = 'account-circle';
            break;
          default:
            iconName = 'circle';
        }
        return <Icon name={iconName} size={size} color={color} />;
      },
      tabBarActiveTintColor: colors.primary,
      tabBarInactiveTintColor: colors.textSecondary,
      headerShown: false,
    })}
  >
    <Tab.Screen name="Home" component={HomeScreen} />
    <Tab.Screen name="Materials" component={MaterialsScreen} />
    <Tab.Screen name="Workers" component={WorkersScreen} />
    <Tab.Screen name="Orders" component={OrdersScreen} />
    <Tab.Screen name="Profile" component={ProfileScreen} />
  </Tab.Navigator>
);

// Vendor Tab Navigator
const VendorTabs = () => (
  <Tab.Navigator
    screenOptions={({ route }) => ({
      tabBarIcon: ({ focused, color, size }) => {
        let iconName;
        switch (route.name) {
          case 'Dashboard':
            iconName = 'dashboard';
            break;
          case 'Orders':
            iconName = 'shopping-cart';
            break;
          case 'Materials':
            iconName = 'inventory';
            break;
          case 'Profile':
            iconName = 'account-circle';
            break;
          default:
            iconName = 'circle';
        }
        return <Icon name={iconName} size={size} color={color} />;
      },
      tabBarActiveTintColor: colors.primary,
      tabBarInactiveTintColor: colors.textSecondary,
      headerShown: false,
    })}
  >
    <Tab.Screen name="Dashboard" component={VendorDashboardScreen} />
    <Tab.Screen name="Orders" component={VendorOrdersScreen} />
    <Tab.Screen name="Materials" component={VendorMaterialsScreen} />
    <Tab.Screen name="Profile" component={VendorProfileScreen} />
  </Tab.Navigator>
);

// Worker Tab Navigator
const WorkerTabs = () => (
  <Tab.Navigator
    screenOptions={({ route }) => ({
      tabBarIcon: ({ focused, color, size }) => {
        let iconName;
        switch (route.name) {
          case 'Dashboard':
            iconName = 'dashboard';
            break;
          case 'Bookings':
            iconName = 'event';
            break;
          case 'Profile':
            iconName = 'account-circle';
            break;
          default:
            iconName = 'circle';
        }
        return <Icon name={iconName} size={size} color={color} />;
      },
      tabBarActiveTintColor: colors.primary,
      tabBarInactiveTintColor: colors.textSecondary,
      headerShown: false,
    })}
  >
    <Tab.Screen name="Dashboard" component={WorkerDashboardScreen} />
    <Tab.Screen name="Bookings" component={WorkerBookingsScreen} />
    <Tab.Screen name="Profile" component={WorkerProfileScreen} />
  </Tab.Navigator>
);

// Auth Navigator
const AuthNavigator = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="Login" component={LoginScreen} />
    <Stack.Screen name="Register" component={RegisterScreen} />
  </Stack.Navigator>
);

// Main App Navigator
const AppNavigator = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <SplashScreen />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {!user ? (
        <Stack.Screen name="Auth" component={AuthNavigator} />
      ) : (
        <>
          {/* Role-based main screens */}
          {user.role === 'vendor' && (
            <Stack.Screen name="VendorMain" component={VendorTabs} />
          )}
          {user.role === 'worker' && (
            <Stack.Screen name="WorkerMain" component={WorkerTabs} />
          )}
          {user.role === 'user' && (
            <Stack.Screen name="UserMain" component={UserTabs} />
          )}
          
          {/* Common screens */}
          <Stack.Screen name="MaterialDetails" component={MaterialDetailsScreen} />
          <Stack.Screen name="WorkerDetails" component={WorkerDetailsScreen} />
          <Stack.Screen name="Cart" component={CartScreen} />
          <Stack.Screen name="OrderDetails" component={OrderDetailsScreen} />
          <Stack.Screen name="Booking" component={BookingScreen} />
          <Stack.Screen name="Payment" component={PaymentScreen} />
          <Stack.Screen name="Tracking" component={TrackingScreen} />
          <Stack.Screen name="Categories" component={CategoriesScreen} />
          <Stack.Screen name="Search" component={SearchScreen} />
          <Stack.Screen name="Notifications" component={NotificationsScreen} />
          <Stack.Screen name="Settings" component={SettingsScreen} />
        </>
      )}
    </Stack.Navigator>
  );
};

const App = () => {
  const [initializing, setInitializing] = useState(true);

  useEffect(() => {
    // Request notification permissions
    const requestNotificationPermission = async () => {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('Authorization status:', authStatus);
        
        // Get FCM token
        const token = await messaging().getToken();
        console.log('FCM Token:', token);
        
        // Save token to AsyncStorage
        await AsyncStorage.setItem('fcmToken', token);
      }
    };

    // Handle background messages
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      console.log('Message handled in the background!', remoteMessage);
    });

    // Handle foreground messages
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('A new FCM message arrived!', remoteMessage);
      
      // Show local notification
      PushNotification.localNotification({
        title: remoteMessage.notification?.title,
        message: remoteMessage.notification?.body,
        playSound: true,
        soundName: 'default',
      });
    });

    requestNotificationPermission();
    setInitializing(false);

    return unsubscribe;
  }, []);

  if (initializing) {
    return <SplashScreen />;
  }

  return (
    <AuthProvider>
      <CartProvider>
        <NavigationContainer>
          <StatusBar
            barStyle="light-content"
            backgroundColor={colors.primary}
            translucent={false}
          />
          <AppNavigator />
          <Toast />
        </NavigationContainer>
      </CartProvider>
    </AuthProvider>
  );
};

export default App;
