{"name": "buildbid-mobile", "version": "1.0.0", "description": "BuildBid Mobile App - Construction Platform", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace BuildBid.xcworkspace -scheme BuildBid -configuration Release archive"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-vector-icons": "^10.0.2", "react-native-maps": "^1.8.0", "react-native-geolocation-service": "^5.3.1", "react-native-permissions": "^3.10.1", "react-native-image-picker": "^7.0.3", "react-native-async-storage": "^1.19.5", "axios": "^1.6.0", "react-native-toast-message": "^2.1.6", "react-native-modal": "^13.0.1", "react-native-star-rating": "^1.1.0", "react-native-razorpay": "^2.3.0", "@react-native-firebase/app": "^18.6.1", "@react-native-firebase/messaging": "^18.6.1", "react-native-push-notification": "^8.1.1", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.11.1", "react-native-elements": "^3.4.3", "react-native-chart-kit": "^6.12.0", "react-native-calendars": "^1.1301.0", "react-native-swiper": "^1.6.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}