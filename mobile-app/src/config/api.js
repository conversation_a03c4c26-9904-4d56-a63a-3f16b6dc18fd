import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Toast from 'react-native-toast-message';

// API Configuration
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:5000/api'  // Development
  : 'https://your-production-api.com/api';  // Production

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.log('Error getting auth token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const { response } = error;
    
    if (response?.status === 401) {
      // Token expired or invalid
      await AsyncStorage.removeItem('authToken');
      await AsyncStorage.removeItem('userData');
      
      Toast.show({
        type: 'error',
        text1: 'Session Expired',
        text2: 'Please login again',
      });
      
      // Navigate to login screen
      // NavigationService.navigate('Login');
    } else if (response?.status >= 500) {
      Toast.show({
        type: 'error',
        text1: 'Server Error',
        text2: 'Please try again later',
      });
    } else if (response?.data?.message) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: response.data.message,
      });
    }
    
    return Promise.reject(error);
  }
);

// API Methods
export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/me'),
};

export const materialsAPI = {
  getAll: (params) => api.get('/materials', { params }),
  getById: (id) => api.get(`/materials/${id}`),
  getCategories: () => api.get('/categories'),
  searchByLocation: (latitude, longitude, radius) => 
    api.get('/materials', { 
      params: { latitude, longitude, radius } 
    }),
};

export const ordersAPI = {
  create: (orderData) => api.post('/orders', orderData),
  getAll: (params) => api.get('/orders', { params }),
  getById: (id) => api.get(`/orders/${id}`),
  updateStatus: (id, status) => api.put(`/orders/${id}/status`, { status }),
  cancelOrder: (id, reason) => api.put(`/orders/${id}/cancel`, { reason }),
};

export const workersAPI = {
  getAll: (params) => api.get('/workers', { params }),
  getById: (id) => api.get(`/workers/${id}`),
  searchBySkill: (skills, location) => 
    api.get('/workers', { 
      params: { skills: skills.join(','), ...location } 
    }),
  bookWorker: (bookingData) => api.post('/bookings', bookingData),
  getBookings: () => api.get('/bookings'),
};

export const paymentsAPI = {
  createOrder: (orderId) => api.post('/payments/create-order', { order_id: orderId }),
  verifyPayment: (paymentData) => api.post('/payments/verify-order', paymentData),
  createBookingOrder: (bookingId) => api.post('/payments/create-booking-order', { booking_id: bookingId }),
  verifyBookingPayment: (paymentData) => api.post('/payments/verify-booking', paymentData),
};

export const userAPI = {
  updateProfile: (userData) => api.put('/users/profile', userData),
  changePassword: (passwordData) => api.post('/users/change-password', passwordData),
  uploadAvatar: (avatarUrl) => api.post('/users/upload-avatar', { avatar_url: avatarUrl }),
};

export const vendorAPI = {
  getAll: (params) => api.get('/vendors', { params }),
  getById: (id) => api.get(`/vendors/${id}`),
  updateProfile: (vendorData) => api.put('/vendors/profile', vendorData),
  getDashboardStats: () => api.get('/vendors/dashboard/stats'),
};

// Utility functions
export const setAuthToken = async (token) => {
  try {
    await AsyncStorage.setItem('authToken', token);
  } catch (error) {
    console.log('Error saving auth token:', error);
  }
};

export const getAuthToken = async () => {
  try {
    return await AsyncStorage.getItem('authToken');
  } catch (error) {
    console.log('Error getting auth token:', error);
    return null;
  }
};

export const removeAuthToken = async () => {
  try {
    await AsyncStorage.removeItem('authToken');
    await AsyncStorage.removeItem('userData');
  } catch (error) {
    console.log('Error removing auth token:', error);
  }
};

export default api;
