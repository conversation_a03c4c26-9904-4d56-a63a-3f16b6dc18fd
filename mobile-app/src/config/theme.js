// BuildBid App Theme Configuration

export const colors = {
  // Primary Colors
  primary: '#FF6B35',        // Orange - Construction theme
  primaryDark: '#E55A2B',    // Darker orange
  primaryLight: '#FFE5DC',   // Light orange
  
  // Secondary Colors
  secondary: '#2E86AB',      // Blue - Trust and reliability
  secondaryDark: '#1E5F7A',  // Darker blue
  secondaryLight: '#E3F2FD', // Light blue
  
  // Status Colors
  success: '#4CAF50',        // Green
  warning: '#FF9800',        // Amber
  error: '#F44336',          // Red
  info: '#2196F3',           // Blue
  
  // Neutral Colors
  white: '#FFFFFF',
  black: '#000000',
  background: '#F8F9FA',     // Light gray background
  surface: '#FFFFFF',        // Card/surface background
  
  // Text Colors
  text: '#212121',           // Primary text
  textSecondary: '#757575',  // Secondary text
  textLight: '#BDBDBD',      // Light text
  textOnPrimary: '#FFFFFF',  // Text on primary color
  
  // Border & Shadow
  border: '#E0E0E0',         // Border color
  shadow: '#000000',         // Shadow color
  
  // Construction Specific Colors
  cement: '#8D6E63',         // Brown for cement
  brick: '#D84315',          // Red-orange for bricks
  steel: '#607D8B',          // Blue-gray for steel
  paint: '#9C27B0',          // Purple for paints
  
  // Status Indicators
  available: '#4CAF50',      // Green for available
  busy: '#FF9800',           // Orange for busy
  offline: '#9E9E9E',        // Gray for offline
};

export const fonts = {
  // Font Families (adjust based on your font setup)
  regular: 'System',
  medium: 'System',
  bold: 'System',
  light: 'System',
  
  // Font Sizes
  sizes: {
    xs: 10,
    sm: 12,
    md: 14,
    lg: 16,
    xl: 18,
    xxl: 20,
    xxxl: 24,
    heading: 28,
    title: 32,
  },
  
  // Line Heights
  lineHeights: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  huge: 48,
};

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  round: 50,
};

export const shadows = {
  small: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  medium: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  large: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

export const layout = {
  // Screen padding
  screenPadding: spacing.lg,
  
  // Card padding
  cardPadding: spacing.md,
  
  // Button heights
  buttonHeight: {
    small: 32,
    medium: 44,
    large: 56,
  },
  
  // Input heights
  inputHeight: 48,
  
  // Header height
  headerHeight: 56,
  
  // Tab bar height
  tabBarHeight: 60,
};

// Component Styles
export const componentStyles = {
  // Button styles
  button: {
    primary: {
      backgroundColor: colors.primary,
      borderRadius: borderRadius.md,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.xl,
    },
    secondary: {
      backgroundColor: colors.secondary,
      borderRadius: borderRadius.md,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.xl,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colors.primary,
      borderRadius: borderRadius.md,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.xl,
    },
  },
  
  // Card styles
  card: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    ...shadows.medium,
  },
  
  // Input styles
  input: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    height: layout.inputHeight,
    fontSize: fonts.sizes.md,
    color: colors.text,
  },
};

// Theme object for easy access
export const theme = {
  colors,
  fonts,
  spacing,
  borderRadius,
  shadows,
  layout,
  componentStyles,
};

export default theme;
