import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Toast from 'react-native-toast-message';

const CartContext = createContext({});

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadCartFromStorage();
  }, []);

  useEffect(() => {
    saveCartToStorage();
  }, [cartItems]);

  const loadCartFromStorage = async () => {
    try {
      const savedCart = await AsyncStorage.getItem('cartItems');
      if (savedCart) {
        setCartItems(JSON.parse(savedCart));
      }
    } catch (error) {
      console.log('Error loading cart from storage:', error);
    }
  };

  const saveCartToStorage = async () => {
    try {
      await AsyncStorage.setItem('cartItems', JSON.stringify(cartItems));
    } catch (error) {
      console.log('Error saving cart to storage:', error);
    }
  };

  const addToCart = (material, quantity = 1) => {
    const existingItemIndex = cartItems.findIndex(
      item => item.material.id === material.id && item.material.vendor_id === material.vendor_id
    );

    if (existingItemIndex >= 0) {
      // Update quantity if item already exists
      const updatedItems = [...cartItems];
      updatedItems[existingItemIndex].quantity += quantity;
      setCartItems(updatedItems);
      
      Toast.show({
        type: 'success',
        text1: 'Updated Cart',
        text2: `${material.name} quantity updated`,
      });
    } else {
      // Add new item to cart
      const newItem = {
        id: `${material.id}_${Date.now()}`,
        material,
        quantity,
        unitPrice: material.final_price,
        totalPrice: material.final_price * quantity,
      };
      
      setCartItems([...cartItems, newItem]);
      
      Toast.show({
        type: 'success',
        text1: 'Added to Cart',
        text2: `${material.name} added successfully`,
      });
    }
  };

  const removeFromCart = (itemId) => {
    const updatedItems = cartItems.filter(item => item.id !== itemId);
    setCartItems(updatedItems);
    
    Toast.show({
      type: 'info',
      text1: 'Removed from Cart',
      text2: 'Item removed successfully',
    });
  };

  const updateQuantity = (itemId, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    const updatedItems = cartItems.map(item => {
      if (item.id === itemId) {
        return {
          ...item,
          quantity: newQuantity,
          totalPrice: item.unitPrice * newQuantity,
        };
      }
      return item;
    });
    
    setCartItems(updatedItems);
  };

  const clearCart = () => {
    setCartItems([]);
    Toast.show({
      type: 'info',
      text1: 'Cart Cleared',
      text2: 'All items removed from cart',
    });
  };

  const getCartTotal = () => {
    return cartItems.reduce((total, item) => total + item.totalPrice, 0);
  };

  const getCartItemsCount = () => {
    return cartItems.reduce((count, item) => count + item.quantity, 0);
  };

  const getCartByVendor = () => {
    const vendorGroups = {};
    
    cartItems.forEach(item => {
      const vendorId = item.material.vendor_id;
      if (!vendorGroups[vendorId]) {
        vendorGroups[vendorId] = {
          vendor: item.material.vendor,
          items: [],
          subtotal: 0,
        };
      }
      
      vendorGroups[vendorId].items.push(item);
      vendorGroups[vendorId].subtotal += item.totalPrice;
    });
    
    return Object.values(vendorGroups);
  };

  const isInCart = (materialId) => {
    return cartItems.some(item => item.material.id === materialId);
  };

  const getItemQuantity = (materialId) => {
    const item = cartItems.find(item => item.material.id === materialId);
    return item ? item.quantity : 0;
  };

  const validateCart = () => {
    // Check if all items are from the same vendor (for single vendor checkout)
    const vendors = [...new Set(cartItems.map(item => item.material.vendor_id))];
    
    if (vendors.length > 1) {
      return {
        valid: false,
        message: 'Cart contains items from multiple vendors. Please checkout separately.',
      };
    }
    
    // Check minimum order quantities
    for (const item of cartItems) {
      if (item.quantity < item.material.min_order_quantity) {
        return {
          valid: false,
          message: `Minimum order quantity for ${item.material.name} is ${item.material.min_order_quantity}`,
        };
      }
    }
    
    // Check stock availability
    for (const item of cartItems) {
      if (item.quantity > item.material.stock_quantity) {
        return {
          valid: false,
          message: `Only ${item.material.stock_quantity} units of ${item.material.name} available`,
        };
      }
    }
    
    return { valid: true };
  };

  const value = {
    cartItems,
    loading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getCartTotal,
    getCartItemsCount,
    getCartByVendor,
    isInCart,
    getItemQuantity,
    validateCart,
    isEmpty: cartItems.length === 0,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
