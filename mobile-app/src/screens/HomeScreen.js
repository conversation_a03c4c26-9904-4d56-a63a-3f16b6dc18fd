import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { materialsAPI, workersAPI } from '../config/api';
import { colors, fonts } from '../config/theme';

const { width } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  const [categories, setCategories] = useState([]);
  const [featuredMaterials, setFeaturedMaterials] = useState([]);
  const [topWorkers, setTopWorkers] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      setLoading(true);
      
      // Load categories, featured materials, and top workers
      const [categoriesRes, materialsRes, workersRes] = await Promise.all([
        materialsAPI.getCategories(),
        materialsAPI.getAll({ limit: 6, featured: true }),
        workersAPI.getAll({ limit: 4, sort: 'rating' })
      ]);

      setCategories(categoriesRes.data.data.categories || []);
      setFeaturedMaterials(materialsRes.data.data.materials || []);
      setTopWorkers(workersRes.data.data.workers || []);
    } catch (error) {
      console.log('Error loading home data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadHomeData();
  };

  const renderCategory = ({ item }) => (
    <TouchableOpacity
      style={styles.categoryCard}
      onPress={() => navigation.navigate('Materials', { categoryId: item.id })}
    >
      <View style={styles.categoryIcon}>
        <Icon name="category" size={30} color={colors.primary} />
      </View>
      <Text style={styles.categoryName}>{item.name}</Text>
      <Text style={styles.categoryNameHindi}>{item.name_hindi}</Text>
    </TouchableOpacity>
  );

  const renderMaterial = ({ item }) => (
    <TouchableOpacity
      style={styles.materialCard}
      onPress={() => navigation.navigate('MaterialDetails', { materialId: item.id })}
    >
      <Image
        source={{ uri: item.images?.[0] || 'https://via.placeholder.com/150' }}
        style={styles.materialImage}
      />
      <View style={styles.materialInfo}>
        <Text style={styles.materialName} numberOfLines={2}>{item.name}</Text>
        <Text style={styles.materialVendor}>{item.vendor?.business_name}</Text>
        <View style={styles.materialPriceRow}>
          <Text style={styles.materialPrice}>₹{item.final_price}/{item.unit}</Text>
          <View style={styles.ratingContainer}>
            <Icon name="star" size={14} color={colors.warning} />
            <Text style={styles.rating}>{item.rating}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderWorker = ({ item }) => (
    <TouchableOpacity
      style={styles.workerCard}
      onPress={() => navigation.navigate('WorkerDetails', { workerId: item.id })}
    >
      <Image
        source={{ uri: item.user?.avatar || 'https://via.placeholder.com/80' }}
        style={styles.workerAvatar}
      />
      <View style={styles.workerInfo}>
        <Text style={styles.workerName}>{item.user?.name}</Text>
        <Text style={styles.workerSkills}>{item.skills?.slice(0, 2).join(', ')}</Text>
        <Text style={styles.workerRate}>₹{item.hourly_rate}/hour</Text>
        <View style={styles.workerRating}>
          <Icon name="star" size={14} color={colors.warning} />
          <Text style={styles.rating}>{item.rating}</Text>
          <Text style={styles.experience}>• {item.experience_years}y exp</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <LinearGradient
          colors={[colors.primary, colors.primaryDark]}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.greeting}>नमस्ते! 🙏</Text>
              <Text style={styles.headerTitle}>BuildBid में आपका स्वागत है</Text>
            </View>
            <TouchableOpacity
              style={styles.profileButton}
              onPress={() => navigation.navigate('Profile')}
            >
              <Icon name="account-circle" size={32} color={colors.white} />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.navigate('Materials')}
          >
            <Icon name="build" size={24} color={colors.white} />
            <Text style={styles.actionText}>सामग्री ऑर्डर करें</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.secondary }]}
            onPress={() => navigation.navigate('Workers')}
          >
            <Icon name="person" size={24} color={colors.white} />
            <Text style={styles.actionText}>मिस्त्री बुक करें</Text>
          </TouchableOpacity>
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>श्रेणियां</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Categories')}>
              <Text style={styles.seeAll}>सभी देखें</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={categories.slice(0, 6)}
            renderItem={renderCategory}
            keyExtractor={(item) => item.id.toString()}
            numColumns={3}
            scrollEnabled={false}
            contentContainerStyle={styles.categoriesGrid}
          />
        </View>

        {/* Featured Materials */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>फीचर्ड सामग्री</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Materials')}>
              <Text style={styles.seeAll}>सभी देखें</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={featuredMaterials}
            renderItem={renderMaterial}
            keyExtractor={(item) => item.id.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalList}
          />
        </View>

        {/* Top Workers */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>टॉप मिस्त्री</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Workers')}>
              <Text style={styles.seeAll}>सभी देखें</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={topWorkers}
            renderItem={renderWorker}
            keyExtractor={(item) => item.id.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalList}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greeting: {
    fontSize: 16,
    color: colors.white,
    fontFamily: fonts.medium,
  },
  headerTitle: {
    fontSize: 20,
    color: colors.white,
    fontFamily: fonts.bold,
    marginTop: 4,
  },
  profileButton: {
    padding: 4,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 15,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 12,
    gap: 8,
  },
  actionText: {
    color: colors.white,
    fontSize: 14,
    fontFamily: fonts.medium,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: fonts.bold,
    color: colors.text,
  },
  seeAll: {
    fontSize: 14,
    color: colors.primary,
    fontFamily: fonts.medium,
  },
  categoriesGrid: {
    gap: 15,
  },
  categoryCard: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: colors.white,
    padding: 15,
    borderRadius: 12,
    marginHorizontal: 5,
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  categoryIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primaryLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 12,
    fontFamily: fonts.medium,
    color: colors.text,
    textAlign: 'center',
  },
  categoryNameHindi: {
    fontSize: 10,
    fontFamily: fonts.regular,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 2,
  },
  horizontalList: {
    paddingRight: 20,
  },
  materialCard: {
    width: 160,
    backgroundColor: colors.white,
    borderRadius: 12,
    marginRight: 15,
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  materialImage: {
    width: '100%',
    height: 100,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  materialInfo: {
    padding: 12,
  },
  materialName: {
    fontSize: 14,
    fontFamily: fonts.medium,
    color: colors.text,
    marginBottom: 4,
  },
  materialVendor: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 8,
  },
  materialPriceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  materialPrice: {
    fontSize: 14,
    fontFamily: fonts.bold,
    color: colors.primary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  rating: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  workerCard: {
    width: 200,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 15,
    marginRight: 15,
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  workerAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 10,
  },
  workerInfo: {
    flex: 1,
  },
  workerName: {
    fontSize: 16,
    fontFamily: fonts.medium,
    color: colors.text,
    marginBottom: 4,
  },
  workerSkills: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  workerRate: {
    fontSize: 14,
    fontFamily: fonts.bold,
    color: colors.primary,
    marginBottom: 6,
  },
  workerRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  experience: {
    fontSize: 12,
    color: colors.textSecondary,
  },
});

export default HomeScreen;
