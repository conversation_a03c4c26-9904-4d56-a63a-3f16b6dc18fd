import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Switch, List, Text, Divider } from 'react-native-paper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import Toast from 'react-native-toast-message';
import api from '../config/api';

const NotificationSettingsScreen = () => {
  const [settings, setSettings] = useState({
    orderUpdates: true,
    paymentAlerts: true,
    workerBookings: true,
    promotionalOffers: false,
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('notificationSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  };

  const updateSettings = async (key, value) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      await AsyncStorage.setItem('notificationSettings', JSON.stringify(newSettings));

      if (Object.values(newSettings).some(setting => setting)) {
        // If any notification type is enabled, ensure FCM token is up to date
        const token = await messaging().getToken();
        await api.post('/users/update-fcm-token', { fcm_token: token });
      } else {
        // If all notifications are disabled, remove FCM token
        await api.post('/users/update-fcm-token', { fcm_token: null });
      }

      Toast.show({
        type: 'success',
        text1: 'Settings Updated',
        text2: 'Your notification preferences have been saved',
      });
    } catch (error) {
      console.error('Error updating notification settings:', error);
      Toast.show({
        type: 'error',
        text1: 'Update Failed',
        text2: 'Failed to save notification settings',
      });
    }
  };

  const requestPermission = async () => {
    try {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (!enabled) {
        Toast.show({
          type: 'error',
          text1: 'Permission Required',
          text2: 'Please enable notifications in your device settings',
        });
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notification Preferences</Text>
        <Text style={styles.sectionDescription}>
          Choose which notifications you'd like to receive
        </Text>
      </View>

      <List.Section>
        <List.Item
          title="Order Updates"
          description="Get notified about your order status"
          left={props => <List.Icon {...props} icon="package" />}
          right={() => (
            <Switch
              value={settings.orderUpdates}
              onValueChange={value => updateSettings('orderUpdates', value)}
            />
          )}
        />
        <Divider />

        <List.Item
          title="Payment Alerts"
          description="Receive payment confirmation notifications"
          left={props => <List.Icon {...props} icon="cash" />}
          right={() => (
            <Switch
              value={settings.paymentAlerts}
              onValueChange={value => updateSettings('paymentAlerts', value)}
            />
          )}
        />
        <Divider />

        <List.Item
          title="Worker Bookings"
          description="Updates about your worker bookings"
          left={props => <List.Icon {...props} icon="account-hard-hat" />}
          right={() => (
            <Switch
              value={settings.workerBookings}
              onValueChange={value => updateSettings('workerBookings', value)}
            />
          )}
        />
        <Divider />

        <List.Item
          title="Promotional Offers"
          description="Get notified about deals and discounts"
          left={props => <List.Icon {...props} icon="tag" />}
          right={() => (
            <Switch
              value={settings.promotionalOffers}
              onValueChange={value => updateSettings('promotionalOffers', value)}
            />
          )}
        />
      </List.Section>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
});

export default NotificationSettingsScreen;