import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { useAuth } from '../../context/AuthContext';
import { colors, fonts, spacing } from '../../config/theme';

const LoginScreen = ({ navigation }) => {
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState('');
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [selectedRole, setSelectedRole] = useState('user');
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const { login } = useAuth();

  const roles = [
    { id: 'user', name: 'User', nameHindi: 'उपयोगकर्ता', icon: 'person', color: colors.primary },
    { id: 'vendor', name: 'Vendor', nameHindi: 'विक्रेता', icon: 'store', color: colors.secondary },
    { id: 'worker', name: 'Worker', nameHindi: 'मिस्त्री', icon: 'build', color: colors.success },
  ];

  const handleSendOtp = async () => {
    if (!phone || phone.length !== 10) {
      Alert.alert('Error', 'Please enter a valid 10-digit phone number');
      return;
    }

    setLoading(true);
    try {
      // Simulate OTP sending
      setTimeout(() => {
        setShowOtpInput(true);
        setLoading(false);
        Alert.alert('OTP Sent', `OTP sent to +91${phone}`);
      }, 1000);
    } catch (error) {
      setLoading(false);
      Alert.alert('Error', 'Failed to send OTP');
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp || otp.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit OTP');
      return;
    }

    setLoading(true);
    try {
      // Simulate OTP verification and login
      const userData = {
        id: Date.now(),
        name: 'User Name',
        phone: `+91${phone}`,
        role: selectedRole,
        language: selectedLanguage,
      };
      
      await login(userData, 'mock_token_' + Date.now());
      
      // Navigate based on role
      if (selectedRole === 'vendor') {
        navigation.replace('VendorDashboard');
      } else if (selectedRole === 'worker') {
        navigation.replace('WorkerDashboard');
      } else {
        navigation.replace('MainTabs');
      }
    } catch (error) {
      Alert.alert('Error', 'Invalid OTP');
    } finally {
      setLoading(false);
    }
  };

  const toggleLanguage = () => {
    setSelectedLanguage(selectedLanguage === 'en' ? 'hi' : 'en');
  };

  const getText = (en, hi) => selectedLanguage === 'hi' ? hi : en;

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[colors.primary, colors.primaryDark]}
        style={styles.header}
      >
        {/* Language Toggle */}
        <TouchableOpacity style={styles.languageToggle} onPress={toggleLanguage}>
          <Text style={styles.languageText}>
            {selectedLanguage === 'en' ? 'हिंदी' : 'English'}
          </Text>
        </TouchableOpacity>

        {/* Logo */}
        <View style={styles.logoContainer}>
          <View style={styles.logoCircle}>
            <Text style={styles.logoText}>BB</Text>
          </View>
          <Text style={styles.appName}>BuildBid</Text>
          <Text style={styles.tagline}>
            {getText('Construction Made Easy', 'निर्माण का भरोसेमंद साथी')}
          </Text>
        </View>
      </LinearGradient>

      <View style={styles.formContainer}>
        {/* Role Selector */}
        <TouchableOpacity
          style={styles.roleSelector}
          onPress={() => setShowRoleModal(true)}
        >
          <View style={styles.roleInfo}>
            <Icon 
              name={roles.find(r => r.id === selectedRole)?.icon} 
              size={24} 
              color={roles.find(r => r.id === selectedRole)?.color} 
            />
            <Text style={styles.roleText}>
              {getText(
                roles.find(r => r.id === selectedRole)?.name,
                roles.find(r => r.id === selectedRole)?.nameHindi
              )}
            </Text>
          </View>
          <Icon name="keyboard-arrow-down" size={24} color={colors.textSecondary} />
        </TouchableOpacity>

        {/* Phone Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            {getText('Mobile Number', 'मोबाइल नंबर')}
          </Text>
          <View style={styles.phoneInputContainer}>
            <Text style={styles.countryCode}>+91</Text>
            <TextInput
              style={styles.phoneInput}
              placeholder={getText('Enter 10-digit number', '10 अंकों का नंबर दर्ज करें')}
              value={phone}
              onChangeText={setPhone}
              keyboardType="numeric"
              maxLength={10}
              editable={!showOtpInput}
            />
          </View>
        </View>

        {/* OTP Input */}
        {showOtpInput && (
          <View style={styles.inputContainer}>
            <Text style={styles.label}>
              {getText('Enter OTP', 'OTP दर्ज करें')}
            </Text>
            <TextInput
              style={styles.otpInput}
              placeholder="000000"
              value={otp}
              onChangeText={setOtp}
              keyboardType="numeric"
              maxLength={6}
              textAlign="center"
            />
            <TouchableOpacity style={styles.resendButton}>
              <Text style={styles.resendText}>
                {getText('Resend OTP', 'OTP फिर से भेजें')}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Action Button */}
        <TouchableOpacity
          style={[styles.actionButton, loading && styles.disabledButton]}
          onPress={showOtpInput ? handleVerifyOtp : handleSendOtp}
          disabled={loading}
        >
          <LinearGradient
            colors={[colors.primary, colors.primaryDark]}
            style={styles.buttonGradient}
          >
            <Text style={styles.buttonText}>
              {loading 
                ? getText('Please wait...', 'कृपया प्रतीक्षा करें...')
                : showOtpInput 
                  ? getText('Verify & Login', 'सत्यापित करें और लॉगिन करें')
                  : getText('Send OTP', 'OTP भेजें')
              }
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        {/* Terms */}
        <Text style={styles.termsText}>
          {getText(
            'By continuing, you agree to our Terms & Privacy Policy',
            'जारी रखकर, आप हमारी शर्तों और गोपनीयता नीति से सहमत हैं'
          )}
        </Text>
      </View>

      {/* Role Selection Modal */}
      <Modal
        visible={showRoleModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowRoleModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              {getText('Select Your Role', 'अपनी भूमिका चुनें')}
            </Text>
            
            {roles.map((role) => (
              <TouchableOpacity
                key={role.id}
                style={[
                  styles.roleOption,
                  selectedRole === role.id && styles.selectedRole
                ]}
                onPress={() => {
                  setSelectedRole(role.id);
                  setShowRoleModal(false);
                }}
              >
                <Icon name={role.icon} size={24} color={role.color} />
                <View style={styles.roleTextContainer}>
                  <Text style={styles.roleName}>
                    {getText(role.name, role.nameHindi)}
                  </Text>
                </View>
                {selectedRole === role.id && (
                  <Icon name="check-circle" size={24} color={colors.success} />
                )}
              </TouchableOpacity>
            ))}
            
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowRoleModal(false)}
            >
              <Text style={styles.modalCloseText}>
                {getText('Close', 'बंद करें')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingTop: spacing.xl,
    paddingBottom: spacing.xxxl,
    alignItems: 'center',
  },
  languageToggle: {
    position: 'absolute',
    top: spacing.lg,
    right: spacing.lg,
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: 20,
  },
  languageText: {
    color: colors.white,
    fontSize: 14,
    fontFamily: fonts.medium,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: spacing.xl,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  logoText: {
    fontSize: 32,
    fontFamily: fonts.bold,
    color: colors.primary,
  },
  appName: {
    fontSize: 28,
    fontFamily: fonts.bold,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  tagline: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.9,
  },
  formContainer: {
    flex: 1,
    padding: spacing.xl,
    backgroundColor: colors.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -20,
  },
  roleSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    marginBottom: spacing.xl,
  },
  roleInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roleText: {
    marginLeft: spacing.md,
    fontSize: 16,
    fontFamily: fonts.medium,
    color: colors.text,
  },
  inputContainer: {
    marginBottom: spacing.xl,
  },
  label: {
    fontSize: 14,
    fontFamily: fonts.medium,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
  },
  countryCode: {
    fontSize: 16,
    fontFamily: fonts.medium,
    color: colors.text,
    marginRight: spacing.sm,
  },
  phoneInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: colors.text,
  },
  otpInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    height: 50,
    fontSize: 20,
    fontFamily: fonts.bold,
    letterSpacing: 8,
  },
  resendButton: {
    alignSelf: 'flex-end',
    marginTop: spacing.sm,
  },
  resendText: {
    color: colors.primary,
    fontSize: 14,
    fontFamily: fonts.medium,
  },
  actionButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: spacing.xl,
  },
  disabledButton: {
    opacity: 0.7,
  },
  buttonGradient: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontFamily: fonts.bold,
  },
  termsText: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 18,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: spacing.xl,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: fonts.bold,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  roleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
    borderRadius: 12,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedRole: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight,
  },
  roleTextContainer: {
    flex: 1,
    marginLeft: spacing.md,
  },
  roleName: {
    fontSize: 16,
    fontFamily: fonts.medium,
    color: colors.text,
  },
  modalCloseButton: {
    marginTop: spacing.lg,
    padding: spacing.lg,
    alignItems: 'center',
  },
  modalCloseText: {
    color: colors.textSecondary,
    fontSize: 16,
    fontFamily: fonts.medium,
  },
});

export default LoginScreen;
