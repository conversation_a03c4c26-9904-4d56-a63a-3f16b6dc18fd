const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Material = sequelize.define('Material', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  vendor_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'vendors',
      key: 'id'
    }
  },
  category_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [2, 200]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  brand: {
    type: DataTypes.STRING,
    allowNull: true
  },
  unit: {
    type: DataTypes.ENUM('kg', 'piece', 'bag', 'ton', 'liter', 'meter', 'sqft', 'cubic_meter'),
    allowNull: false
  },
  price_per_unit: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  discount_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100
    }
  },
  final_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  stock_quantity: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  min_order_quantity: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    allowNull: false
  },
  max_order_quantity: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  specifications: {
    type: DataTypes.JSON,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  is_featured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0,
    allowNull: false
  },
  total_ratings: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  total_sold: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  weight_per_unit: {
    type: DataTypes.DECIMAL(8, 3),
    allowNull: true
  },
  dimensions: {
    type: DataTypes.JSON,
    allowNull: true
  },
  warranty_period: {
    type: DataTypes.STRING,
    allowNull: true
  },
  return_policy: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'materials',
  hooks: {
    beforeSave: (material) => {
      // Calculate final price after discount
      const discount = material.price_per_unit * (material.discount_percentage / 100);
      material.final_price = material.price_per_unit - discount;
    }
  }
});

module.exports = Material;
