const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const OrderItem = sequelize.define('OrderItem', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  order_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'orders',
      key: 'id'
    }
  },
  material_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'materials',
      key: 'id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 1
    }
  },
  unit_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  total_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  material_snapshot: {
    type: DataTypes.JSON,
    allowNull: true
  }
}, {
  tableName: 'order_items',
  hooks: {
    beforeSave: (orderItem) => {
      orderItem.total_price = orderItem.quantity * orderItem.unit_price;
    }
  }
});

module.exports = OrderItem;
