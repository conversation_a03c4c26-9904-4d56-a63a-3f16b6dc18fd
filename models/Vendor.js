const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Vendor = sequelize.define('Vendor', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  business_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [2, 200]
    }
  },
  business_type: {
    type: DataTypes.ENUM('materials', 'tools', 'both'),
    defaultValue: 'materials',
    allowNull: false
  },
  gst_number: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [15, 15]
    }
  },
  business_license: {
    type: DataTypes.STRING,
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  business_address: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  business_city: {
    type: DataTypes.STRING,
    allowNull: false
  },
  business_state: {
    type: DataTypes.STRING,
    allowNull: false
  },
  business_pincode: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [6, 6]
    }
  },
  business_latitude: {
    type: DataTypes.DECIMAL(10, 8),
    allowNull: true
  },
  business_longitude: {
    type: DataTypes.DECIMAL(11, 8),
    allowNull: true
  },
  delivery_radius: {
    type: DataTypes.INTEGER,
    defaultValue: 10, // in kilometers
    allowNull: false
  },
  min_order_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    allowNull: false
  },
  delivery_charges: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    allowNull: false
  },
  free_delivery_above: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0,
    allowNull: false
  },
  total_ratings: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  total_orders: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  is_verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  verification_status: {
    type: DataTypes.ENUM('pending', 'verified', 'rejected'),
    defaultValue: 'pending'
  },
  verification_documents: {
    type: DataTypes.JSON,
    allowNull: true
  },
  business_hours: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      monday: { open: '09:00', close: '18:00', closed: false },
      tuesday: { open: '09:00', close: '18:00', closed: false },
      wednesday: { open: '09:00', close: '18:00', closed: false },
      thursday: { open: '09:00', close: '18:00', closed: false },
      friday: { open: '09:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '18:00', closed: false },
      sunday: { open: '10:00', close: '17:00', closed: false }
    }
  },
  bank_details: {
    type: DataTypes.JSON,
    allowNull: true
  }
}, {
  tableName: 'vendors'
});

module.exports = Vendor;
