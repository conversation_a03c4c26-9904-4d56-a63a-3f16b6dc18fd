const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Worker = sequelize.define('Worker', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  skills: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  experience_years: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 0,
      max: 50
    }
  },
  hourly_rate: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  daily_rate: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  availability: {
    type: DataTypes.ENUM('available', 'busy', 'unavailable'),
    defaultValue: 'available'
  },
  work_radius: {
    type: DataTypes.INTEGER,
    defaultValue: 15, // in kilometers
    allowNull: false
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0,
    allowNull: false
  },
  total_ratings: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  total_jobs: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  completed_jobs: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  portfolio_images: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  certifications: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  tools_owned: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  languages: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: ['Hindi', 'English']
  },
  work_preferences: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      preferred_work_days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
      preferred_work_hours: { start: '08:00', end: '18:00' },
      willing_to_travel: true,
      minimum_job_duration: 4 // hours
    }
  },
  identity_documents: {
    type: DataTypes.JSON,
    allowNull: true
  },
  bank_details: {
    type: DataTypes.JSON,
    allowNull: true
  },
  emergency_contact: {
    type: DataTypes.JSON,
    allowNull: true
  },
  is_verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  verification_status: {
    type: DataTypes.ENUM('pending', 'verified', 'rejected'),
    defaultValue: 'pending'
  },
  bio: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  specializations: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  }
}, {
  tableName: 'workers'
});

module.exports = Worker;
