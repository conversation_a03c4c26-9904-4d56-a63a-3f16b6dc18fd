const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const WorkerBooking = sequelize.define('WorkerBooking', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  booking_number: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  worker_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'workers',
      key: 'id'
    }
  },
  work_type: {
    type: DataTypes.STRING,
    allowNull: false
  },
  work_description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  work_address: {
    type: DataTypes.JSON,
    allowNull: false
  },
  scheduled_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  scheduled_time: {
    type: DataTypes.TIME,
    allowNull: false
  },
  estimated_duration: {
    type: DataTypes.INTEGER, // in hours
    allowNull: false
  },
  hourly_rate: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: false
  },
  estimated_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  actual_duration: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  actual_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM(
      'pending', 'accepted', 'rejected', 'in_progress', 
      'completed', 'cancelled', 'payment_pending'
    ),
    defaultValue: 'pending',
    allowNull: false
  },
  payment_status: {
    type: DataTypes.ENUM('pending', 'paid', 'failed', 'refunded'),
    defaultValue: 'pending',
    allowNull: false
  },
  payment_method: {
    type: DataTypes.ENUM('cod', 'online', 'wallet'),
    allowNull: false
  },
  work_images_before: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  work_images_after: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  materials_required: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  tools_required: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  special_instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  worker_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  start_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  end_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    }
  },
  review: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  worker_rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    }
  },
  worker_review: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  cancellation_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  cancelled_by: {
    type: DataTypes.ENUM('user', 'worker', 'admin'),
    allowNull: true
  },
  razorpay_order_id: {
    type: DataTypes.STRING,
    allowNull: true
  },
  razorpay_payment_id: {
    type: DataTypes.STRING,
    allowNull: true
  }
}, {
  tableName: 'worker_bookings',
  hooks: {
    beforeCreate: async (booking) => {
      if (!booking.booking_number) {
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        booking.booking_number = `WB${timestamp.slice(-6)}${random}`;
      }
    }
  }
});

module.exports = WorkerBooking;
