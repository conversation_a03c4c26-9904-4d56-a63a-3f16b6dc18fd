const { sequelize } = require('../config/database');

// Import all models
const User = require('./User');
const Vendor = require('./Vendor');
const Category = require('./Category');
const Material = require('./Material');
const Order = require('./Order');
const OrderItem = require('./OrderItem');
const Worker = require('./Worker');
const WorkerBooking = require('./WorkerBooking');

// Define associations

// User associations
User.hasOne(Vendor, { foreignKey: 'user_id', as: 'vendor_profile' });
User.hasOne(Worker, { foreignKey: 'user_id', as: 'worker_profile' });
User.hasMany(Order, { foreignKey: 'user_id', as: 'orders' });
User.hasMany(WorkerBooking, { foreignKey: 'user_id', as: 'bookings' });

// Vendor associations
Vendor.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Vendor.hasMany(Material, { foreignKey: 'vendor_id', as: 'materials' });
Vendor.hasMany(Order, { foreignKey: 'vendor_id', as: 'orders' });

// Category associations
Category.hasMany(Material, { foreignKey: 'category_id', as: 'materials' });

// Material associations
Material.belongsTo(Vendor, { foreignKey: 'vendor_id', as: 'vendor' });
Material.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });
Material.hasMany(OrderItem, { foreignKey: 'material_id', as: 'order_items' });

// Order associations
Order.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Order.belongsTo(Vendor, { foreignKey: 'vendor_id', as: 'vendor' });
Order.hasMany(OrderItem, { foreignKey: 'order_id', as: 'items' });

// OrderItem associations
OrderItem.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
OrderItem.belongsTo(Material, { foreignKey: 'material_id', as: 'material' });

// Worker associations
Worker.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Worker.hasMany(WorkerBooking, { foreignKey: 'worker_id', as: 'bookings' });

// WorkerBooking associations
WorkerBooking.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
WorkerBooking.belongsTo(Worker, { foreignKey: 'worker_id', as: 'worker' });

// Export all models
module.exports = {
  sequelize,
  User,
  Vendor,
  Category,
  Material,
  Order,
  OrderItem,
  Worker,
  WorkerBooking
};
