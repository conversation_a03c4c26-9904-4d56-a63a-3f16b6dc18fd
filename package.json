{"name": "buildbid-backend", "version": "1.0.0", "description": "BuildBid - Construction Platform Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test-server": "node test-server.js", "migrate": "node scripts/initDatabase.js", "seed": "node scripts/seedCategories.js"}, "keywords": ["construction", "materials", "workers", "platform"], "author": "BuildBid Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.32.1", "multer": "^1.4.5-lts.1", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "winston": "^3.10.0", "firebase-admin": "^11.10.1", "razorpay": "^2.9.2", "socket.io": "^4.7.2", "aws-sdk": "^2.1450.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3"}}