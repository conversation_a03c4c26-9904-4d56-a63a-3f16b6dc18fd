const express = require('express');
const { Op } = require('sequelize');
const { User, Vendor, Worker, Order, Material, Category } = require('../models');
const { auth, authorize } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// @route   GET /api/admin/dashboard
// @desc    Get admin dashboard statistics
// @access  Private (Admin)
router.get('/dashboard', auth, authorize('admin'), async (req, res) => {
  try {
    // Get basic counts
    const totalUsers = await User.count({ where: { role: 'user' } });
    const totalVendors = await Vendor.count();
    const totalWorkers = await Worker.count();
    const totalOrders = await Order.count();
    const totalMaterials = await Material.count();

    // Get pending verifications
    const pendingVendors = await Vendor.count({ where: { verification_status: 'pending' } });
    const pendingWorkers = await Worker.count({ where: { verification_status: 'pending' } });

    // Get revenue statistics
    const totalRevenue = await Order.sum('total_amount', {
      where: { payment_status: 'paid' }
    }) || 0;

    const monthlyRevenue = await Order.sum('total_amount', {
      where: {
        payment_status: 'paid',
        created_at: {
          [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      }
    }) || 0;

    // Get recent activities
    const recentOrders = await Order.findAll({
      include: [
        { model: User, as: 'user', attributes: ['name'] },
        { model: Vendor, as: 'vendor', include: [{ model: User, as: 'user', attributes: ['name'] }] }
      ],
      order: [['created_at', 'DESC']],
      limit: 10
    });

    const recentUsers = await User.findAll({
      where: { role: { [Op.ne]: 'admin' } },
      order: [['created_at', 'DESC']],
      limit: 10,
      attributes: ['id', 'name', 'email', 'role', 'created_at', 'is_active']
    });

    const stats = {
      overview: {
        total_users: totalUsers,
        total_vendors: totalVendors,
        total_workers: totalWorkers,
        total_orders: totalOrders,
        total_materials: totalMaterials,
        total_revenue: totalRevenue,
        monthly_revenue: monthlyRevenue
      },
      pending_verifications: {
        vendors: pendingVendors,
        workers: pendingWorkers
      },
      recent_activities: {
        orders: recentOrders,
        users: recentUsers
      }
    };

    res.json({
      success: true,
      data: { stats }
    });
  } catch (error) {
    logger.error('Get admin dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/users
// @desc    Get all users with filters
// @access  Private (Admin)
router.get('/users', auth, authorize('admin'), async (req, res) => {
  try {
    const { page = 1, limit = 20, role, status, search } = req.query;
    const offset = (page - 1) * limit;
    
    const where = {};
    
    if (role && role !== 'all') {
      where.role = role;
    }
    
    if (status === 'active') {
      where.is_active = true;
    } else if (status === 'inactive') {
      where.is_active = false;
    }
    
    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { phone: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const { count, rows: users } = await User.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      attributes: { exclude: ['password'] }
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get admin users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/admin/users/:id/status
// @desc    Update user status (activate/deactivate)
// @access  Private (Admin)
router.put('/users/:id/status', auth, authorize('admin'), async (req, res) => {
  try {
    const { is_active } = req.body;
    
    if (typeof is_active !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'is_active must be a boolean value'
      });
    }

    const user = await User.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.role === 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Cannot modify admin user status'
      });
    }

    await user.update({ is_active });

    logger.info(`User status updated: ${user.email} - ${is_active ? 'activated' : 'deactivated'} by admin: ${req.user.email}`);

    res.json({
      success: true,
      message: `User ${is_active ? 'activated' : 'deactivated'} successfully`
    });
  } catch (error) {
    logger.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/vendors/pending
// @desc    Get pending vendor verifications
// @access  Private (Admin)
router.get('/vendors/pending', auth, authorize('admin'), async (req, res) => {
  try {
    const pendingVendors = await Vendor.findAll({
      where: { verification_status: 'pending' },
      include: [{
        model: User,
        as: 'user',
        attributes: ['name', 'email', 'phone']
      }],
      order: [['created_at', 'ASC']]
    });

    res.json({
      success: true,
      data: { vendors: pendingVendors }
    });
  } catch (error) {
    logger.error('Get pending vendors error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/admin/vendors/:id/verify
// @desc    Verify or reject vendor
// @access  Private (Admin)
router.put('/vendors/:id/verify', auth, authorize('admin'), async (req, res) => {
  try {
    const { status, rejection_reason } = req.body;
    
    if (!['verified', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Status must be either verified or rejected'
      });
    }

    const vendor = await Vendor.findByPk(req.params.id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    const updateData = { 
      verification_status: status,
      is_verified: status === 'verified'
    };

    if (status === 'rejected' && rejection_reason) {
      updateData.rejection_reason = rejection_reason;
    }

    await vendor.update(updateData);

    logger.info(`Vendor verification updated: ${vendor.id} - ${status} by admin: ${req.user.email}`);

    res.json({
      success: true,
      message: `Vendor ${status} successfully`
    });
  } catch (error) {
    logger.error('Verify vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/workers/pending
// @desc    Get pending worker verifications
// @access  Private (Admin)
router.get('/workers/pending', auth, authorize('admin'), async (req, res) => {
  try {
    const pendingWorkers = await Worker.findAll({
      where: { verification_status: 'pending' },
      include: [{
        model: User,
        as: 'user',
        attributes: ['name', 'email', 'phone']
      }],
      order: [['created_at', 'ASC']]
    });

    res.json({
      success: true,
      data: { workers: pendingWorkers }
    });
  } catch (error) {
    logger.error('Get pending workers error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/admin/workers/:id/verify
// @desc    Verify or reject worker
// @access  Private (Admin)
router.put('/workers/:id/verify', auth, authorize('admin'), async (req, res) => {
  try {
    const { status, rejection_reason } = req.body;
    
    if (!['verified', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Status must be either verified or rejected'
      });
    }

    const worker = await Worker.findByPk(req.params.id);
    if (!worker) {
      return res.status(404).json({
        success: false,
        message: 'Worker not found'
      });
    }

    const updateData = { 
      verification_status: status,
      is_verified: status === 'verified'
    };

    if (status === 'rejected' && rejection_reason) {
      updateData.rejection_reason = rejection_reason;
    }

    await worker.update(updateData);

    logger.info(`Worker verification updated: ${worker.id} - ${status} by admin: ${req.user.email}`);

    res.json({
      success: true,
      message: `Worker ${status} successfully`
    });
  } catch (error) {
    logger.error('Verify worker error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
