const express = require('express');
const { Op, sequelize } = require('sequelize');
const { body, query, validationResult } = require('express-validator');
const { Material, Vendor, Category, User } = require('../models');
const { auth, authorize, optionalAuth } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// @route   GET /api/materials
// @desc    Get all materials with filters
// @access  Public
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1-50'),
  query('category').optional().isUUID().withMessage('Invalid category ID'),
  query('vendor').optional().isUUID().withMessage('Invalid vendor ID'),
  query('search').optional().isLength({ min: 2 }).withMessage('Search term must be at least 2 characters'),
  query('min_price').optional().isFloat({ min: 0 }).withMessage('Min price must be positive'),
  query('max_price').optional().isFloat({ min: 0 }).withMessage('Max price must be positive'),
  query('sort').optional().isIn(['price_asc', 'price_desc', 'rating', 'newest']).withMessage('Invalid sort option')
], optionalAuth, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      category,
      vendor,
      search,
      min_price,
      max_price,
      sort = 'newest',
      latitude,
      longitude,
      radius = 10
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { is_active: true };
    const include = [
      {
        model: Vendor,
        as: 'vendor',
        where: { is_active: true, is_verified: true },
        include: [{
          model: User,
          as: 'user',
          attributes: ['name', 'phone']
        }]
      },
      {
        model: Category,
        as: 'category',
        where: { is_active: true }
      }
    ];

    // Apply filters
    if (category) {
      where.category_id = category;
    }

    if (vendor) {
      where.vendor_id = vendor;
    }

    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { brand: { [Op.iLike]: `%${search}%` } }
      ];
    }

    if (min_price || max_price) {
      where.final_price = {};
      if (min_price) where.final_price[Op.gte] = min_price;
      if (max_price) where.final_price[Op.lte] = max_price;
    }

    // Location-based filtering
    if (latitude && longitude) {
      include[0].where = {
        ...include[0].where,
        [Op.and]: [
          sequelize.where(
            sequelize.fn(
              'ST_DWithin',
              sequelize.fn('ST_Point', sequelize.col('vendor.business_longitude'), sequelize.col('vendor.business_latitude')),
              sequelize.fn('ST_Point', longitude, latitude),
              radius * 1000 // Convert km to meters
            ),
            true
          )
        ]
      };
    }

    // Sorting
    let order = [];
    switch (sort) {
      case 'price_asc':
        order = [['final_price', 'ASC']];
        break;
      case 'price_desc':
        order = [['final_price', 'DESC']];
        break;
      case 'rating':
        order = [['rating', 'DESC'], ['total_ratings', 'DESC']];
        break;
      case 'newest':
      default:
        order = [['created_at', 'DESC']];
        break;
    }

    const { count, rows: materials } = await Material.findAndCountAll({
      where,
      include,
      order,
      limit: parseInt(limit),
      offset: parseInt(offset),
      distinct: true
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        materials,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get materials error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/materials/:id
// @desc    Get single material
// @access  Public
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const material = await Material.findOne({
      where: { id: req.params.id, is_active: true },
      include: [
        {
          model: Vendor,
          as: 'vendor',
          where: { is_active: true },
          include: [{
            model: User,
            as: 'user',
            attributes: ['name', 'phone', 'city', 'state']
          }]
        },
        {
          model: Category,
          as: 'category'
        }
      ]
    });

    if (!material) {
      return res.status(404).json({
        success: false,
        message: 'Material not found'
      });
    }

    res.json({
      success: true,
      data: { material }
    });
  } catch (error) {
    logger.error('Get material error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/materials
// @desc    Create material (Vendor only)
// @access  Private
router.post('/', [
  auth,
  authorize('vendor'),
  body('name').trim().isLength({ min: 2, max: 200 }).withMessage('Name must be between 2-200 characters'),
  body('category_id').isUUID().withMessage('Invalid category ID'),
  body('unit').isIn(['kg', 'piece', 'bag', 'ton', 'liter', 'meter', 'sqft', 'cubic_meter']).withMessage('Invalid unit'),
  body('price_per_unit').isFloat({ min: 0 }).withMessage('Price must be positive'),
  body('stock_quantity').isInt({ min: 0 }).withMessage('Stock quantity must be non-negative'),
  body('min_order_quantity').optional().isInt({ min: 1 }).withMessage('Min order quantity must be positive')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Get vendor profile
    const vendor = await Vendor.findOne({ where: { user_id: req.user.id } });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    if (!vendor.is_verified) {
      return res.status(403).json({
        success: false,
        message: 'Vendor account not verified'
      });
    }

    const materialData = {
      ...req.body,
      vendor_id: vendor.id
    };

    const material = await Material.create(materialData);

    const createdMaterial = await Material.findByPk(material.id, {
      include: [
        { model: Vendor, as: 'vendor' },
        { model: Category, as: 'category' }
      ]
    });

    logger.info(`Material created: ${material.name} by vendor: ${vendor.id}`);

    res.status(201).json({
      success: true,
      message: 'Material created successfully',
      data: { material: createdMaterial }
    });
  } catch (error) {
    logger.error('Create material error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
