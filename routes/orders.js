const express = require('express');
const { body, validationResult } = require('express-validator');
const { Order, OrderItem, Material, Vendor, User } = require('../models');
const { auth, authorize } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// @route   POST /api/orders
// @desc    Create new order
// @access  Private (User)
router.post('/', [
  auth,
  authorize('user'),
  body('vendor_id').isUUID().withMessage('Invalid vendor ID'),
  body('items').isArray({ min: 1 }).withMessage('Order must have at least one item'),
  body('items.*.material_id').isUUID().withMessage('Invalid material ID'),
  body('items.*.quantity').isInt({ min: 1 }).withMessage('Quantity must be positive'),
  body('delivery_address').notEmpty().withMessage('Delivery address is required'),
  body('payment_method').isIn(['cod', 'online', 'wallet']).withMessage('Invalid payment method')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { vendor_id, items, delivery_address, delivery_instructions, payment_method } = req.body;

    // Verify vendor exists and is active
    const vendor = await Vendor.findOne({
      where: { id: vendor_id, is_active: true, is_verified: true }
    });

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found or not available'
      });
    }

    // Verify all materials exist and calculate totals
    let subtotal = 0;
    const orderItems = [];

    for (const item of items) {
      const material = await Material.findOne({
        where: { 
          id: item.material_id, 
          vendor_id: vendor_id,
          is_active: true 
        }
      });

      if (!material) {
        return res.status(404).json({
          success: false,
          message: `Material with ID ${item.material_id} not found`
        });
      }

      // Check stock availability
      if (material.stock_quantity < item.quantity) {
        return res.status(400).json({
          success: false,
          message: `Insufficient stock for ${material.name}. Available: ${material.stock_quantity}`
        });
      }

      // Check minimum order quantity
      if (item.quantity < material.min_order_quantity) {
        return res.status(400).json({
          success: false,
          message: `Minimum order quantity for ${material.name} is ${material.min_order_quantity}`
        });
      }

      const itemTotal = material.final_price * item.quantity;
      subtotal += itemTotal;

      orderItems.push({
        material_id: material.id,
        quantity: item.quantity,
        unit_price: material.final_price,
        total_price: itemTotal,
        material_snapshot: {
          name: material.name,
          brand: material.brand,
          unit: material.unit,
          images: material.images
        }
      });
    }

    // Check minimum order amount
    if (subtotal < vendor.min_order_amount) {
      return res.status(400).json({
        success: false,
        message: `Minimum order amount is ₹${vendor.min_order_amount}`
      });
    }

    // Calculate delivery charges
    let delivery_charges = vendor.delivery_charges;
    if (vendor.free_delivery_above && subtotal >= vendor.free_delivery_above) {
      delivery_charges = 0;
    }

    // Calculate tax (assuming 18% GST)
    const tax_amount = subtotal * 0.18;
    const total_amount = subtotal + delivery_charges + tax_amount;

    // Create order
    const order = await Order.create({
      user_id: req.user.id,
      vendor_id: vendor_id,
      subtotal,
      delivery_charges,
      tax_amount,
      total_amount,
      delivery_address,
      delivery_instructions,
      payment_method,
      estimated_delivery_time: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    });

    // Create order items
    for (const item of orderItems) {
      await OrderItem.create({
        ...item,
        order_id: order.id
      });
    }

    // Update material stock
    for (const item of items) {
      await Material.decrement('stock_quantity', {
        by: item.quantity,
        where: { id: item.material_id }
      });
    }

    // Fetch complete order with items
    const completeOrder = await Order.findByPk(order.id, {
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [{
            model: Material,
            as: 'material'
          }]
        },
        {
          model: Vendor,
          as: 'vendor',
          include: [{
            model: User,
            as: 'user',
            attributes: ['name', 'phone']
          }]
        }
      ]
    });

    // Send notification to vendor (implement Firebase notification here)
    const io = req.app.get('io');
    io.to(`vendor_${vendor_id}`).emit('new_order', {
      order_id: order.id,
      order_number: order.order_number,
      customer_name: req.user.name,
      total_amount: total_amount
    });

    logger.info(`Order created: ${order.order_number} by user: ${req.user.id}`);

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: { order: completeOrder }
    });
  } catch (error) {
    logger.error('Create order error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/orders
// @desc    Get user's orders
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const offset = (page - 1) * limit;
    
    const where = {};
    
    // Filter by user role
    if (req.user.role === 'user') {
      where.user_id = req.user.id;
    } else if (req.user.role === 'vendor') {
      const vendor = await Vendor.findOne({ where: { user_id: req.user.id } });
      if (!vendor) {
        return res.status(404).json({
          success: false,
          message: 'Vendor profile not found'
        });
      }
      where.vendor_id = vendor.id;
    }

    if (status) {
      where.status = status;
    }

    const { count, rows: orders } = await Order.findAndCountAll({
      where,
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [{
            model: Material,
            as: 'material'
          }]
        },
        {
          model: User,
          as: 'user',
          attributes: ['name', 'phone', 'email']
        },
        {
          model: Vendor,
          as: 'vendor',
          include: [{
            model: User,
            as: 'user',
            attributes: ['name', 'phone']
          }]
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/orders/:id
// @desc    Get single order
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id, {
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [{
            model: Material,
            as: 'material'
          }]
        },
        {
          model: User,
          as: 'user',
          attributes: ['name', 'phone', 'email']
        },
        {
          model: Vendor,
          as: 'vendor',
          include: [{
            model: User,
            as: 'user',
            attributes: ['name', 'phone']
          }]
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check authorization
    const isAuthorized = 
      req.user.role === 'admin' ||
      order.user_id === req.user.id ||
      (req.user.role === 'vendor' && order.vendor.user_id === req.user.id);

    if (!isAuthorized) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: { order }
    });
  } catch (error) {
    logger.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
