const express = require('express');
const { Op } = require('sequelize');
const { body, validationResult } = require('express-validator');
const { Vendor, User, Material, Order } = require('../models');
const { auth, authorize } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// @route   GET /api/vendors
// @desc    Get all vendors
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 20, city, search, latitude, longitude, radius = 10 } = req.query;
    const offset = (page - 1) * limit;
    
    const where = { is_active: true, is_verified: true };
    const include = [{
      model: User,
      as: 'user',
      attributes: ['name', 'phone', 'avatar']
    }];

    // City filter
    if (city) {
      where.business_city = { [Op.iLike]: `%${city}%` };
    }

    // Search filter
    if (search) {
      where[Op.or] = [
        { business_name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Location-based filtering (if coordinates provided)
    if (latitude && longitude) {
      // This would require PostGIS extension for proper geo queries
      // For now, we'll use a simple distance calculation
    }

    const { count, rows: vendors } = await Vendor.findAndCountAll({
      where,
      include,
      order: [['rating', 'DESC'], ['total_orders', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        vendors,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get vendors error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/vendors/:id
// @desc    Get single vendor
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const vendor = await Vendor.findOne({
      where: { id: req.params.id, is_active: true },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['name', 'phone', 'avatar', 'city', 'state']
        },
        {
          model: Material,
          as: 'materials',
          where: { is_active: true },
          required: false,
          limit: 10,
          order: [['created_at', 'DESC']]
        }
      ]
    });

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    res.json({
      success: true,
      data: { vendor }
    });
  } catch (error) {
    logger.error('Get vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/vendors/profile
// @desc    Update vendor profile
// @access  Private (Vendor)
router.put('/profile', [
  auth,
  authorize('vendor'),
  body('business_name').optional().trim().isLength({ min: 2, max: 200 }).withMessage('Business name must be between 2-200 characters'),
  body('business_type').optional().isIn(['materials', 'tools', 'both']).withMessage('Invalid business type'),
  body('description').optional().trim().isLength({ max: 1000 }).withMessage('Description too long'),
  body('business_address').optional().trim().notEmpty().withMessage('Business address is required'),
  body('delivery_radius').optional().isInt({ min: 1, max: 100 }).withMessage('Delivery radius must be between 1-100 km'),
  body('min_order_amount').optional().isFloat({ min: 0 }).withMessage('Min order amount must be positive'),
  body('delivery_charges').optional().isFloat({ min: 0 }).withMessage('Delivery charges must be positive')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const vendor = await Vendor.findOne({ where: { user_id: req.user.id } });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    const allowedUpdates = [
      'business_name', 'business_type', 'description', 'business_address',
      'business_city', 'business_state', 'business_pincode', 'business_latitude',
      'business_longitude', 'delivery_radius', 'min_order_amount', 'delivery_charges',
      'free_delivery_above', 'business_hours', 'gst_number'
    ];

    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    await vendor.update(updates);

    const updatedVendor = await Vendor.findByPk(vendor.id, {
      include: [{
        model: User,
        as: 'user',
        attributes: ['name', 'phone', 'email']
      }]
    });

    logger.info(`Vendor profile updated: ${vendor.id}`);

    res.json({
      success: true,
      message: 'Vendor profile updated successfully',
      data: { vendor: updatedVendor }
    });
  } catch (error) {
    logger.error('Update vendor profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/vendors/dashboard/stats
// @desc    Get vendor dashboard statistics
// @access  Private (Vendor)
router.get('/dashboard/stats', auth, authorize('vendor'), async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ where: { user_id: req.user.id } });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Get basic stats
    const totalMaterials = await Material.count({
      where: { vendor_id: vendor.id, is_active: true }
    });

    const totalOrders = await Order.count({
      where: { vendor_id: vendor.id }
    });

    const pendingOrders = await Order.count({
      where: { vendor_id: vendor.id, status: 'pending' }
    });

    const completedOrders = await Order.count({
      where: { vendor_id: vendor.id, status: 'delivered' }
    });

    // Calculate total revenue
    const revenueResult = await Order.sum('total_amount', {
      where: { 
        vendor_id: vendor.id, 
        payment_status: 'paid',
        status: 'delivered'
      }
    });

    const totalRevenue = revenueResult || 0;

    // Get recent orders
    const recentOrders = await Order.findAll({
      where: { vendor_id: vendor.id },
      include: [{
        model: User,
        as: 'user',
        attributes: ['name', 'phone']
      }],
      order: [['created_at', 'DESC']],
      limit: 5
    });

    const stats = {
      total_materials: totalMaterials,
      total_orders: totalOrders,
      pending_orders: pendingOrders,
      completed_orders: completedOrders,
      total_revenue: totalRevenue,
      rating: vendor.rating,
      total_ratings: vendor.total_ratings,
      recent_orders: recentOrders
    };

    res.json({
      success: true,
      data: { stats }
    });
  } catch (error) {
    logger.error('Get vendor stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/vendors/upload-documents
// @desc    Upload verification documents
// @access  Private (Vendor)
router.post('/upload-documents', auth, authorize('vendor'), async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ where: { user_id: req.user.id } });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    const { documents } = req.body;
    
    if (!documents || !Array.isArray(documents)) {
      return res.status(400).json({
        success: false,
        message: 'Documents array is required'
      });
    }

    await vendor.update({
      verification_documents: documents,
      verification_status: 'pending'
    });

    logger.info(`Verification documents uploaded for vendor: ${vendor.id}`);

    res.json({
      success: true,
      message: 'Documents uploaded successfully. Verification is pending.'
    });
  } catch (error) {
    logger.error('Upload documents error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
