const express = require('express');
const { Op } = require('sequelize');
const { body, query, validationResult } = require('express-validator');
const { Worker, User, WorkerBooking } = require('../models');
const { auth, authorize, optionalAuth } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// @route   GET /api/workers
// @desc    Get all workers with filters
// @access  Public
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1-50'),
  query('skills').optional().notEmpty().withMessage('Skills filter cannot be empty'),
  query('min_rate').optional().isFloat({ min: 0 }).withMessage('Min rate must be positive'),
  query('max_rate').optional().isFloat({ min: 0 }).withMessage('Max rate must be positive'),
  query('availability').optional().isIn(['available', 'busy', 'unavailable']).withMessage('Invalid availability status')
], optionalAuth, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      skills,
      min_rate,
      max_rate,
      availability,
      latitude,
      longitude,
      radius = 15,
      sort = 'rating'
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { 
      is_active: true, 
      is_verified: true 
    };
    
    const include = [{
      model: User,
      as: 'user',
      attributes: ['name', 'phone', 'avatar', 'city', 'state'],
      where: { is_active: true }
    }];

    // Skills filter
    if (skills) {
      const skillsArray = skills.split(',').map(skill => skill.trim());
      where.skills = {
        [Op.overlap]: skillsArray
      };
    }

    // Rate filters
    if (min_rate || max_rate) {
      where.hourly_rate = {};
      if (min_rate) where.hourly_rate[Op.gte] = min_rate;
      if (max_rate) where.hourly_rate[Op.lte] = max_rate;
    }

    // Availability filter
    if (availability) {
      where.availability = availability;
    }

    // Location-based filtering
    if (latitude && longitude) {
      // This would require PostGIS for proper geo queries
      // For now, we'll use a simple approach
      include[0].where = {
        ...include[0].where,
        latitude: {
          [Op.between]: [
            parseFloat(latitude) - (radius * 0.009), // Rough conversion
            parseFloat(latitude) + (radius * 0.009)
          ]
        },
        longitude: {
          [Op.between]: [
            parseFloat(longitude) - (radius * 0.009),
            parseFloat(longitude) + (radius * 0.009)
          ]
        }
      };
    }

    // Sorting
    let order = [];
    switch (sort) {
      case 'rate_asc':
        order = [['hourly_rate', 'ASC']];
        break;
      case 'rate_desc':
        order = [['hourly_rate', 'DESC']];
        break;
      case 'experience':
        order = [['experience_years', 'DESC']];
        break;
      case 'rating':
      default:
        order = [['rating', 'DESC'], ['total_ratings', 'DESC']];
        break;
    }

    const { count, rows: workers } = await Worker.findAndCountAll({
      where,
      include,
      order,
      limit: parseInt(limit),
      offset: parseInt(offset),
      distinct: true
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        workers,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get workers error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/workers/:id
// @desc    Get single worker
// @access  Public
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const worker = await Worker.findOne({
      where: { id: req.params.id, is_active: true },
      include: [{
        model: User,
        as: 'user',
        attributes: ['name', 'phone', 'avatar', 'city', 'state', 'address']
      }]
    });

    if (!worker) {
      return res.status(404).json({
        success: false,
        message: 'Worker not found'
      });
    }

    // Get recent reviews (limit to 5)
    const recentBookings = await WorkerBooking.findAll({
      where: { 
        worker_id: worker.id, 
        status: 'completed',
        rating: { [Op.ne]: null }
      },
      include: [{
        model: User,
        as: 'user',
        attributes: ['name']
      }],
      order: [['updated_at', 'DESC']],
      limit: 5
    });

    res.json({
      success: true,
      data: { 
        worker,
        recent_reviews: recentBookings
      }
    });
  } catch (error) {
    logger.error('Get worker error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/workers/profile
// @desc    Update worker profile
// @access  Private (Worker)
router.put('/profile', [
  auth,
  authorize('worker'),
  body('skills').optional().isArray().withMessage('Skills must be an array'),
  body('experience_years').optional().isInt({ min: 0, max: 50 }).withMessage('Experience must be between 0-50 years'),
  body('hourly_rate').optional().isFloat({ min: 0 }).withMessage('Hourly rate must be positive'),
  body('daily_rate').optional().isFloat({ min: 0 }).withMessage('Daily rate must be positive'),
  body('work_radius').optional().isInt({ min: 1, max: 100 }).withMessage('Work radius must be between 1-100 km'),
  body('availability').optional().isIn(['available', 'busy', 'unavailable']).withMessage('Invalid availability status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const worker = await Worker.findOne({ where: { user_id: req.user.id } });
    if (!worker) {
      return res.status(404).json({
        success: false,
        message: 'Worker profile not found'
      });
    }

    const allowedUpdates = [
      'skills', 'experience_years', 'hourly_rate', 'daily_rate', 'availability',
      'work_radius', 'portfolio_images', 'certifications', 'tools_owned',
      'languages', 'work_preferences', 'bio', 'specializations'
    ];

    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    await worker.update(updates);

    const updatedWorker = await Worker.findByPk(worker.id, {
      include: [{
        model: User,
        as: 'user',
        attributes: ['name', 'phone', 'email']
      }]
    });

    logger.info(`Worker profile updated: ${worker.id}`);

    res.json({
      success: true,
      message: 'Worker profile updated successfully',
      data: { worker: updatedWorker }
    });
  } catch (error) {
    logger.error('Update worker profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/workers/dashboard/stats
// @desc    Get worker dashboard statistics
// @access  Private (Worker)
router.get('/dashboard/stats', auth, authorize('worker'), async (req, res) => {
  try {
    const worker = await Worker.findOne({ where: { user_id: req.user.id } });
    if (!worker) {
      return res.status(404).json({
        success: false,
        message: 'Worker profile not found'
      });
    }

    const totalBookings = await WorkerBooking.count({
      where: { worker_id: worker.id }
    });

    const pendingBookings = await WorkerBooking.count({
      where: { worker_id: worker.id, status: 'pending' }
    });

    const completedBookings = await WorkerBooking.count({
      where: { worker_id: worker.id, status: 'completed' }
    });

    // Calculate total earnings
    const earningsResult = await WorkerBooking.sum('actual_cost', {
      where: { 
        worker_id: worker.id, 
        payment_status: 'paid',
        status: 'completed'
      }
    });

    const totalEarnings = earningsResult || 0;

    // Get recent bookings
    const recentBookings = await WorkerBooking.findAll({
      where: { worker_id: worker.id },
      include: [{
        model: User,
        as: 'user',
        attributes: ['name', 'phone']
      }],
      order: [['created_at', 'DESC']],
      limit: 5
    });

    const stats = {
      total_bookings: totalBookings,
      pending_bookings: pendingBookings,
      completed_bookings: completedBookings,
      total_earnings: totalEarnings,
      rating: worker.rating,
      total_ratings: worker.total_ratings,
      availability: worker.availability,
      recent_bookings: recentBookings
    };

    res.json({
      success: true,
      data: { stats }
    });
  } catch (error) {
    logger.error('Get worker stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
