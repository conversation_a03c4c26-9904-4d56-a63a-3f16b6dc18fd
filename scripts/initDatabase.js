const { sequelize } = require('../config/database');
const { User } = require('../models');
const seedCategories = require('./seedCategories');
const logger = require('../utils/logger');

const initDatabase = async () => {
  try {
    console.log('🚀 Initializing BuildBid Database...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully');
    
    // Sync all models
    console.log('🔄 Synchronizing database models...');
    await sequelize.sync({ force: false, alter: true });
    console.log('✅ Database models synchronized');
    
    // Create admin user if not exists
    console.log('👤 Creating admin user...');
    const [adminUser, created] = await User.findOrCreate({
      where: { email: '<EMAIL>' },
      defaults: {
        name: 'BuildBid Admin',
        email: '<EMAIL>',
        phone: '9999999999',
        password: 'admin123',
        role: 'admin',
        is_verified: true,
        is_active: true,
        email_verified_at: new Date(),
        phone_verified_at: new Date()
      }
    });
    
    if (created) {
      console.log('✅ Admin user created successfully');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: admin123');
    } else {
      console.log('⚠️  Admin user already exists');
    }
    
    // Seed categories
    await seedCategories();
    
    console.log('🎉 Database initialization completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Copy .env.example to .env and configure your environment variables');
    console.log('2. Start the server: npm run dev');
    console.log('3. Access admin <NAME_EMAIL> / admin123');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    logger.error('Database initialization error:', error);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  initDatabase()
    .then(() => {
      console.log('✅ Initialization completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Initialization failed:', error);
      process.exit(1);
    });
}

module.exports = initDatabase;
