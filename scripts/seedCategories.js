const { Category } = require('../models');
const logger = require('../utils/logger');

const categories = [
  {
    name: 'Cement',
    name_hindi: 'सीमेंट',
    description: 'Various types of cement for construction',
    icon: 'cement-icon',
    is_featured: true,
    sort_order: 1
  },
  {
    name: 'Bricks',
    name_hindi: 'ईंट',
    description: 'Red bricks, fly ash bricks, and other masonry units',
    icon: 'brick-icon',
    is_featured: true,
    sort_order: 2
  },
  {
    name: 'Sand',
    name_hindi: 'रेत',
    description: 'River sand, M-sand, and construction sand',
    icon: 'sand-icon',
    is_featured: true,
    sort_order: 3
  },
  {
    name: 'Aggregates',
    name_hindi: 'बजरी',
    description: 'Stone chips, gravel, and crushed stone',
    icon: 'aggregate-icon',
    is_featured: true,
    sort_order: 4
  },
  {
    name: 'Steel & TMT Bars',
    name_hindi: 'स्टील और टीएमटी बार',
    description: 'TMT bars, steel rods, and reinforcement steel',
    icon: 'steel-icon',
    is_featured: true,
    sort_order: 5
  },
  {
    name: 'Paints',
    name_hindi: 'पेंट',
    description: 'Interior paints, exterior paints, primers',
    icon: 'paint-icon',
    is_featured: true,
    sort_order: 6
  },
  {
    name: 'Tiles',
    name_hindi: 'टाइल्स',
    description: 'Floor tiles, wall tiles, ceramic tiles',
    icon: 'tiles-icon',
    is_featured: true,
    sort_order: 7
  },
  {
    name: 'Plumbing',
    name_hindi: 'प्लंबिंग',
    description: 'Pipes, fittings, sanitary ware',
    icon: 'plumbing-icon',
    is_featured: false,
    sort_order: 8
  },
  {
    name: 'Electrical',
    name_hindi: 'इलेक्ट्रिकल',
    description: 'Wires, switches, electrical fittings',
    icon: 'electrical-icon',
    is_featured: false,
    sort_order: 9
  },
  {
    name: 'Hardware',
    name_hindi: 'हार्डवेयर',
    description: 'Nails, screws, hinges, locks',
    icon: 'hardware-icon',
    is_featured: false,
    sort_order: 10
  },
  {
    name: 'Roofing',
    name_hindi: 'छत',
    description: 'Roofing sheets, tiles, waterproofing materials',
    icon: 'roofing-icon',
    is_featured: false,
    sort_order: 11
  },
  {
    name: 'Doors & Windows',
    name_hindi: 'दरवाजे और खिड़कियां',
    description: 'Wooden doors, UPVC windows, frames',
    icon: 'doors-icon',
    is_featured: false,
    sort_order: 12
  }
];

const seedCategories = async () => {
  try {
    console.log('🌱 Seeding categories...');
    
    for (const categoryData of categories) {
      const [category, created] = await Category.findOrCreate({
        where: { name: categoryData.name },
        defaults: categoryData
      });
      
      if (created) {
        console.log(`✅ Created category: ${category.name}`);
      } else {
        console.log(`⚠️  Category already exists: ${category.name}`);
      }
    }
    
    console.log('🎉 Categories seeding completed!');
  } catch (error) {
    console.error('❌ Error seeding categories:', error);
    logger.error('Seed categories error:', error);
  }
};

module.exports = seedCategories;

// Run if called directly
if (require.main === module) {
  const { sequelize } = require('../config/database');
  
  sequelize.authenticate()
    .then(() => {
      console.log('Database connected successfully');
      return seedCategories();
    })
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('Database connection failed:', error);
      process.exit(1);
    });
}
