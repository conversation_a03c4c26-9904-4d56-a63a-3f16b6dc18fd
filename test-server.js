const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Test routes without database
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    message: 'BuildBid API is running (Test Mode)',
    timestamp: new Date().toISOString(),
    mode: 'test'
  });
});

// Mock API endpoints for testing
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'BuildBid API Test Endpoint',
    data: {
      platform: 'BuildBid Construction Platform',
      version: '1.0.0',
      features: [
        'Material Ordering',
        'Worker Hiring',
        'Real-time Tracking',
        'Payment Integration',
        'Multi-role Support'
      ]
    }
  });
});

// Mock categories endpoint
app.get('/api/categories', (req, res) => {
  res.json({
    success: true,
    data: {
      categories: [
        { id: 1, name: 'Cement', name_hindi: 'सीमेंट', icon: 'cement-icon' },
        { id: 2, name: 'Bricks', name_hindi: 'ईंट', icon: 'brick-icon' },
        { id: 3, name: 'Sand', name_hindi: 'रेत', icon: 'sand-icon' },
        { id: 4, name: 'Steel & TMT Bars', name_hindi: 'स्टील', icon: 'steel-icon' },
        { id: 5, name: 'Paints', name_hindi: 'पेंट', icon: 'paint-icon' }
      ]
    }
  });
});

// Mock materials endpoint
app.get('/api/materials', (req, res) => {
  res.json({
    success: true,
    data: {
      materials: [
        {
          id: 1,
          name: 'ACC Cement',
          category: 'Cement',
          price_per_unit: 350,
          unit: 'bag',
          vendor: 'Sharma Building Materials',
          rating: 4.5,
          in_stock: true
        },
        {
          id: 2,
          name: 'Red Bricks',
          category: 'Bricks',
          price_per_unit: 8,
          unit: 'piece',
          vendor: 'Kumar Brick Works',
          rating: 4.2,
          in_stock: true
        }
      ],
      pagination: {
        current_page: 1,
        total_pages: 1,
        total_items: 2
      }
    }
  });
});

// Mock workers endpoint
app.get('/api/workers', (req, res) => {
  res.json({
    success: true,
    data: {
      workers: [
        {
          id: 1,
          name: 'Ramesh Kumar',
          skills: ['Masonry', 'Plastering'],
          experience_years: 8,
          hourly_rate: 250,
          rating: 4.7,
          availability: 'available',
          location: 'Delhi'
        },
        {
          id: 2,
          name: 'Suresh Singh',
          skills: ['Plumbing', 'Electrical'],
          experience_years: 12,
          hourly_rate: 300,
          rating: 4.8,
          availability: 'available',
          location: 'Gurgaon'
        }
      ],
      pagination: {
        current_page: 1,
        total_pages: 1,
        total_items: 2
      }
    }
  });
});

// Mock auth endpoints
app.post('/api/auth/register', (req, res) => {
  const { name, email, role } = req.body;
  res.status(201).json({
    success: true,
    message: 'User registered successfully (Test Mode)',
    data: {
      user: {
        id: Date.now(),
        name,
        email,
        role: role || 'user',
        is_verified: false
      },
      token: 'test_jwt_token_' + Date.now()
    }
  });
});

app.post('/api/auth/login', (req, res) => {
  const { email } = req.body;
  res.json({
    success: true,
    message: 'Login successful (Test Mode)',
    data: {
      user: {
        id: 1,
        name: 'Test User',
        email,
        role: 'user'
      },
      token: 'test_jwt_token_' + Date.now()
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    message: 'Route not found',
    available_routes: [
      'GET /health',
      'GET /api/test',
      'GET /api/categories',
      'GET /api/materials',
      'GET /api/workers',
      'POST /api/auth/register',
      'POST /api/auth/login'
    ]
  });
});

const PORT = process.env.PORT || 5001;

app.listen(PORT, () => {
  console.log(`🚀 BuildBid Test Server running on port ${PORT}`);
  console.log(`📍 Health Check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test Endpoint: http://localhost:${PORT}/api/test`);
  console.log(`📱 Ready for frontend integration!`);
  console.log(`\n⚠️  Note: This is TEST MODE - no database required`);
  console.log(`   For full functionality, set up PostgreSQL and use 'npm run dev'`);
});

module.exports = app;
