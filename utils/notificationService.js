const admin = require('firebase-admin');
const User = require('../models/User');

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert({
    projectId: process.env.FIREBASE_PROJECT_ID,
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n')
  })
});

class NotificationService {
  static async sendToUser(userId, title, body, data = {}) {
    try {
      const user = await User.findByPk(userId);
      if (!user || !user.fcm_token) return false;

      const message = {
        notification: { title, body },
        data: { ...data },
        token: user.fcm_token
      };

      await admin.messaging().send(message);
      return true;
    } catch (error) {
      console.error('Error sending notification:', error);
      return false;
    }
  }

  static async sendToMultipleUsers(userIds, title, body, data = {}) {
    try {
      const users = await User.findAll({
        where: { id: userIds },
        attributes: ['fcm_token']
      });

      const tokens = users
        .map(user => user.fcm_token)
        .filter(token => token);

      if (tokens.length === 0) return false;

      const message = {
        notification: { title, body },
        data: { ...data },
        tokens
      };

      await admin.messaging().sendMulticast(message);
      return true;
    } catch (error) {
      console.error('Error sending multicast notification:', error);
      return false;
    }
  }

  // Notification templates
  static async notifyOrderPlaced(orderId, vendorId, deliveryPartnerId) {
    // Notify vendor
    await this.sendToUser(
      vendorId,
      'New Order Received',
      'You have received a new order. Check your dashboard for details.',
      { orderId, type: 'new_order' }
    );

    // Notify delivery partner if assigned
    if (deliveryPartnerId) {
      await this.sendToUser(
        deliveryPartnerId,
        'New Pickup Request',
        'You have a new order pickup request.',
        { orderId, type: 'new_pickup' }
      );
    }
  }

  static async notifyOrderStatusUpdate(orderId, userId, status) {
    const statusMessages = {
      confirmed: 'Your order has been confirmed by the vendor',
      processing: 'Your order is being processed',
      shipped: 'Your order is out for delivery',
      delivered: 'Your order has been delivered successfully',
      cancelled: 'Your order has been cancelled'
    };

    await this.sendToUser(
      userId,
      'Order Status Update',
      statusMessages[status] || `Your order status has been updated to ${status}`,
      { orderId, type: 'status_update', status }
    );
  }

  static async notifyWorkerBooking(workerId, bookingId) {
    await this.sendToUser(
      workerId,
      'New Work Request',
      'You have received a new work request. Check your bookings.',
      { bookingId, type: 'new_booking' }
    );
  }

  static async notifyPaymentSuccess(userId, orderId, amount) {
    await this.sendToUser(
      userId,
      'Payment Successful',
      `Your payment of ₹${amount} has been received successfully.`,
      { orderId, type: 'payment_success', amount }
    );
  }
}

module.exports = NotificationService;