{"name": "buildbid-dashboard", "version": "1.0.0", "description": "BuildBid Web Dashboard - Vendor & Admin Panel", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "@mui/material": "^5.11.10", "@mui/icons-material": "^5.11.9", "@mui/x-data-grid": "^5.17.26", "@mui/x-date-pickers": "^5.0.20", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "axios": "^1.3.4", "recharts": "^2.5.0", "react-query": "^3.39.3", "react-hook-form": "^7.43.5", "react-hot-toast": "^2.4.0", "date-fns": "^2.29.3", "lodash": "^4.17.21", "react-dropzone": "^14.2.3", "react-image-crop": "^10.0.9", "react-helmet": "^6.1.0", "react-loading-skeleton": "^3.1.1", "react-infinite-scroll-component": "^6.1.0"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/lodash": "^4.14.191", "typescript": "^4.9.5", "web-vitals": "^3.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:vendor": "REACT_APP_BUILD_TYPE=vendor npm run build", "build:admin": "REACT_APP_BUILD_TYPE=admin npm run build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}