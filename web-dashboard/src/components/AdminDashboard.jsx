import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Tab,
  Tabs,
  Avatar,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as UsersIcon,
  Store as VendorsIcon,
  Build as WorkersIcon,
  TrendingUp as RevenueIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  Block as BlockIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { format } from 'date-fns';
import toast from 'react-hot-toast';

import { adminAPI } from '../config/api';

const AdminDashboard = () => {
  const [stats, setStats] = useState(null);
  const [users, setUsers] = useState([]);
  const [pendingVendors, setPendingVendors] = useState([]);
  const [pendingWorkers, setPendingWorkers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  const [verificationDialogOpen, setVerificationDialogOpen] = useState(false);
  const [selectedVerification, setSelectedVerification] = useState(null);
  const [rejectionReason, setRejectionReason] = useState('');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      const [dashboardRes, usersRes, vendorsRes, workersRes] = await Promise.all([
        adminAPI.getDashboard(),
        adminAPI.getUsers({ limit: 10 }),
        adminAPI.getPendingVendors(),
        adminAPI.getPendingWorkers()
      ]);

      setStats(dashboardRes.data.data.stats);
      setUsers(usersRes.data.data.users);
      setPendingVendors(vendorsRes.data.data.vendors);
      setPendingWorkers(workersRes.data.data.workers);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleUserStatusUpdate = async (userId, isActive) => {
    try {
      await adminAPI.updateUserStatus(userId, isActive);
      toast.success(`User ${isActive ? 'activated' : 'deactivated'} successfully`);
      loadDashboardData();
    } catch (error) {
      console.error('Error updating user status:', error);
      toast.error('Failed to update user status');
    }
  };

  const handleVerification = async (type, id, status) => {
    try {
      if (type === 'vendor') {
        await adminAPI.verifyVendor(id, status, rejectionReason);
      } else {
        await adminAPI.verifyWorker(id, status, rejectionReason);
      }
      
      toast.success(`${type} ${status} successfully`);
      setVerificationDialogOpen(false);
      setRejectionReason('');
      loadDashboardData();
    } catch (error) {
      console.error(`Error ${status} ${type}:`, error);
      toast.error(`Failed to ${status} ${type}`);
    }
  };

  const openVerificationDialog = (type, item, action) => {
    setSelectedVerification({ type, item, action });
    setVerificationDialogOpen(true);
  };

  const getUserRoleColor = (role) => {
    const colors = {
      user: 'primary',
      vendor: 'secondary',
      worker: 'success',
      admin: 'error',
    };
    return colors[role] || 'default';
  };

  const pieChartData = [
    { name: 'Users', value: stats?.overview?.total_users || 0, color: '#FF6B35' },
    { name: 'Vendors', value: stats?.overview?.total_vendors || 0, color: '#2E86AB' },
    { name: 'Workers', value: stats?.overview?.total_workers || 0, color: '#4CAF50' },
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading admin dashboard...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Typography variant="h4" component="h1" fontWeight="bold" mb={3}>
        Admin Dashboard
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#FF6B35', color: 'white' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.overview?.total_users || 0}
                  </Typography>
                  <Typography variant="body2">Total Users</Typography>
                </Box>
                <UsersIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#2E86AB', color: 'white' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.overview?.total_vendors || 0}
                  </Typography>
                  <Typography variant="body2">Total Vendors</Typography>
                </Box>
                <VendorsIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#4CAF50', color: 'white' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.overview?.total_workers || 0}
                  </Typography>
                  <Typography variant="body2">Total Workers</Typography>
                </Box>
                <WorkersIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#FF9800', color: 'white' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    ₹{stats?.overview?.total_revenue?.toLocaleString() || 0}
                  </Typography>
                  <Typography variant="body2">Total Revenue</Typography>
                </Box>
                <RevenueIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Revenue Trend
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={stats?.revenue_trend || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="revenue" stroke="#FF6B35" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                User Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={pieChartData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}`}
                  >
                    {pieChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs for different sections */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)}>
            <Tab label="Recent Users" />
            <Tab label={`Pending Vendors (${pendingVendors.length})`} />
            <Tab label={`Pending Workers (${pendingWorkers.length})`} />
          </Tabs>
        </Box>

        <CardContent>
          {/* Recent Users Tab */}
          {selectedTab === 0 && (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>User</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Role</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Joined</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Avatar src={user.avatar} sx={{ width: 32, height: 32 }}>
                            {user.name.charAt(0)}
                          </Avatar>
                          {user.name}
                        </Box>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Chip
                          label={user.role}
                          color={getUserRoleColor(user.role)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.is_active ? 'Active' : 'Inactive'}
                          color={user.is_active ? 'success' : 'error'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {format(new Date(user.created_at), 'MMM dd, yyyy')}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedUser(user);
                            setUserDialogOpen(true);
                          }}
                        >
                          <ViewIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          color={user.is_active ? 'error' : 'success'}
                          onClick={() => handleUserStatusUpdate(user.id, !user.is_active)}
                        >
                          <BlockIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Pending Vendors Tab */}
          {selectedTab === 1 && (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Business Name</TableCell>
                    <TableCell>Owner</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Phone</TableCell>
                    <TableCell>Applied</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {pendingVendors.map((vendor) => (
                    <TableRow key={vendor.id}>
                      <TableCell>{vendor.business_name}</TableCell>
                      <TableCell>{vendor.user?.name}</TableCell>
                      <TableCell>{vendor.user?.email}</TableCell>
                      <TableCell>{vendor.user?.phone}</TableCell>
                      <TableCell>
                        {format(new Date(vendor.created_at), 'MMM dd, yyyy')}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          color="success"
                          onClick={() => openVerificationDialog('vendor', vendor, 'verified')}
                        >
                          <ApproveIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => openVerificationDialog('vendor', vendor, 'rejected')}
                        >
                          <RejectIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Pending Workers Tab */}
          {selectedTab === 2 && (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Skills</TableCell>
                    <TableCell>Experience</TableCell>
                    <TableCell>Rate</TableCell>
                    <TableCell>Applied</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {pendingWorkers.map((worker) => (
                    <TableRow key={worker.id}>
                      <TableCell>{worker.user?.name}</TableCell>
                      <TableCell>{worker.skills?.join(', ')}</TableCell>
                      <TableCell>{worker.experience_years} years</TableCell>
                      <TableCell>₹{worker.hourly_rate}/hour</TableCell>
                      <TableCell>
                        {format(new Date(worker.created_at), 'MMM dd, yyyy')}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          color="success"
                          onClick={() => openVerificationDialog('worker', worker, 'verified')}
                        >
                          <ApproveIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => openVerificationDialog('worker', worker, 'rejected')}
                        >
                          <RejectIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Verification Dialog */}
      <Dialog
        open={verificationDialogOpen}
        onClose={() => setVerificationDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {selectedVerification?.action === 'verified' ? 'Approve' : 'Reject'} {selectedVerification?.type}
        </DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Are you sure you want to {selectedVerification?.action} this {selectedVerification?.type}?
          </Typography>
          {selectedVerification?.action === 'rejected' && (
            <TextField
              label="Rejection Reason"
              multiline
              rows={3}
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              fullWidth
              margin="normal"
              required
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setVerificationDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={() => handleVerification(
              selectedVerification?.type,
              selectedVerification?.item?.id,
              selectedVerification?.action
            )}
            variant="contained"
            color={selectedVerification?.action === 'verified' ? 'success' : 'error'}
          >
            {selectedVerification?.action === 'verified' ? 'Approve' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminDashboard;
