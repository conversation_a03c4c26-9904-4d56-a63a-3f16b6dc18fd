import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Inventory as InventoryIcon,
  ShoppingCart as OrdersIcon,
  TrendingUp as RevenueIcon,
  Star as StarIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { format } from 'date-fns';
import toast from 'react-hot-toast';

import { vendorAPI, materialsAPI } from '../config/api';

const VendorDashboard = () => {
  const [stats, setStats] = useState(null);
  const [orders, setOrders] = useState([]);
  const [materials, setMaterials] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [orderDialogOpen, setOrderDialogOpen] = useState(false);
  const [materialDialogOpen, setMaterialDialogOpen] = useState(false);
  const [newMaterial, setNewMaterial] = useState({
    name: '',
    category_id: '',
    price_per_unit: '',
    unit: '',
    stock_quantity: '',
    description: '',
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      const [statsRes, ordersRes, materialsRes] = await Promise.all([
        vendorAPI.getDashboardStats(),
        vendorAPI.getOrders({ limit: 10 }),
        materialsAPI.getAll({ limit: 10 })
      ]);

      setStats(statsRes.data.data.stats);
      setOrders(ordersRes.data.data.orders);
      setMaterials(materialsRes.data.data.materials);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleOrderStatusUpdate = async (orderId, newStatus) => {
    try {
      await vendorAPI.updateOrderStatus(orderId, newStatus);
      toast.success('Order status updated successfully');
      loadDashboardData();
      setOrderDialogOpen(false);
    } catch (error) {
      console.error('Error updating order status:', error);
      toast.error('Failed to update order status');
    }
  };

  const handleAddMaterial = async () => {
    try {
      await materialsAPI.create(newMaterial);
      toast.success('Material added successfully');
      setMaterialDialogOpen(false);
      setNewMaterial({
        name: '',
        category_id: '',
        price_per_unit: '',
        unit: '',
        stock_quantity: '',
        description: '',
      });
      loadDashboardData();
    } catch (error) {
      console.error('Error adding material:', error);
      toast.error('Failed to add material');
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'warning',
      confirmed: 'info',
      preparing: 'primary',
      ready_for_pickup: 'secondary',
      out_for_delivery: 'primary',
      delivered: 'success',
      cancelled: 'error',
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      pending: 'Pending',
      confirmed: 'Confirmed',
      preparing: 'Preparing',
      ready_for_pickup: 'Ready for Pickup',
      out_for_delivery: 'Out for Delivery',
      delivered: 'Delivered',
      cancelled: 'Cancelled',
    };
    return texts[status] || status;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading dashboard...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Vendor Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setMaterialDialogOpen(true)}
          sx={{ bgcolor: '#FF6B35' }}
        >
          Add Material
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#FF6B35', color: 'white' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.total_materials || 0}
                  </Typography>
                  <Typography variant="body2">Total Materials</Typography>
                </Box>
                <InventoryIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#2E86AB', color: 'white' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.total_orders || 0}
                  </Typography>
                  <Typography variant="body2">Total Orders</Typography>
                </Box>
                <OrdersIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#4CAF50', color: 'white' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    ₹{stats?.total_revenue?.toLocaleString() || 0}
                  </Typography>
                  <Typography variant="body2">Total Revenue</Typography>
                </Box>
                <RevenueIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#FF9800', color: 'white' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.rating?.toFixed(1) || '0.0'}
                  </Typography>
                  <Typography variant="body2">Average Rating</Typography>
                </Box>
                <StarIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Orders */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Orders
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Order #</TableCell>
                      <TableCell>Customer</TableCell>
                      <TableCell>Amount</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {orders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell>{order.order_number}</TableCell>
                        <TableCell>{order.user?.name}</TableCell>
                        <TableCell>₹{order.total_amount}</TableCell>
                        <TableCell>
                          <Chip
                            label={getStatusText(order.status)}
                            color={getStatusColor(order.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {format(new Date(order.created_at), 'MMM dd, yyyy')}
                        </TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={() => {
                              setSelectedOrder(order);
                              setOrderDialogOpen(true);
                            }}
                          >
                            <ViewIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Stats
              </Typography>
              <Box mb={2}>
                <Typography variant="body2" color="textSecondary">
                  Pending Orders
                </Typography>
                <Typography variant="h5" color="warning.main">
                  {stats?.pending_orders || 0}
                </Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="textSecondary">
                  Completed Orders
                </Typography>
                <Typography variant="h5" color="success.main">
                  {stats?.completed_orders || 0}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="textSecondary">
                  Total Ratings
                </Typography>
                <Typography variant="h5">
                  {stats?.total_ratings || 0}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Order Details Dialog */}
      <Dialog
        open={orderDialogOpen}
        onClose={() => setOrderDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Order Details - {selectedOrder?.order_number}</DialogTitle>
        <DialogContent>
          {selectedOrder && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Customer: {selectedOrder.user?.name}
              </Typography>
              <Typography variant="body1" gutterBottom>
                Phone: {selectedOrder.user?.phone}
              </Typography>
              <Typography variant="body1" gutterBottom>
                Amount: ₹{selectedOrder.total_amount}
              </Typography>
              <Typography variant="body1" gutterBottom>
                Status: {getStatusText(selectedOrder.status)}
              </Typography>
              
              <Box mt={2}>
                <Typography variant="h6" gutterBottom>
                  Update Status:
                </Typography>
                <Box display="flex" gap={1} flexWrap="wrap">
                  {['confirmed', 'preparing', 'ready_for_pickup', 'out_for_delivery', 'delivered'].map((status) => (
                    <Button
                      key={status}
                      variant={selectedOrder.status === status ? 'contained' : 'outlined'}
                      size="small"
                      onClick={() => handleOrderStatusUpdate(selectedOrder.id, status)}
                    >
                      {getStatusText(status)}
                    </Button>
                  ))}
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOrderDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Add Material Dialog */}
      <Dialog
        open={materialDialogOpen}
        onClose={() => setMaterialDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Material</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <TextField
              label="Material Name"
              value={newMaterial.name}
              onChange={(e) => setNewMaterial({ ...newMaterial, name: e.target.value })}
              fullWidth
            />
            <TextField
              label="Price per Unit"
              type="number"
              value={newMaterial.price_per_unit}
              onChange={(e) => setNewMaterial({ ...newMaterial, price_per_unit: e.target.value })}
              fullWidth
            />
            <TextField
              label="Unit"
              select
              value={newMaterial.unit}
              onChange={(e) => setNewMaterial({ ...newMaterial, unit: e.target.value })}
              fullWidth
            >
              <MenuItem value="kg">Kg</MenuItem>
              <MenuItem value="piece">Piece</MenuItem>
              <MenuItem value="bag">Bag</MenuItem>
              <MenuItem value="ton">Ton</MenuItem>
              <MenuItem value="liter">Liter</MenuItem>
              <MenuItem value="meter">Meter</MenuItem>
              <MenuItem value="sqft">Sq Ft</MenuItem>
            </TextField>
            <TextField
              label="Stock Quantity"
              type="number"
              value={newMaterial.stock_quantity}
              onChange={(e) => setNewMaterial({ ...newMaterial, stock_quantity: e.target.value })}
              fullWidth
            />
            <TextField
              label="Description"
              multiline
              rows={3}
              value={newMaterial.description}
              onChange={(e) => setNewMaterial({ ...newMaterial, description: e.target.value })}
              fullWidth
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMaterialDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddMaterial} variant="contained">
            Add Material
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VendorDashboard;
