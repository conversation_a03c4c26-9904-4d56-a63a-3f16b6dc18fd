import axios from 'axios';
import toast from 'react-hot-toast';

// API Configuration
const API_BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:5000/api'
  : 'https://your-production-api.com/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;
    
    if (response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('userData');
      
      toast.error('Session expired. Please login again.');
      
      // Redirect to login
      window.location.href = '/login';
    } else if (response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (response?.data?.message) {
      toast.error(response.data.message);
    } else {
      toast.error('Something went wrong. Please try again.');
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/me'),
};

// Vendor API
export const vendorAPI = {
  getDashboardStats: () => api.get('/vendors/dashboard/stats'),
  updateProfile: (data) => api.put('/vendors/profile', data),
  uploadDocuments: (documents) => api.post('/vendors/upload-documents', { documents }),
  getOrders: (params) => api.get('/orders', { params }),
  updateOrderStatus: (orderId, status) => api.put(`/orders/${orderId}/status`, { status }),
};

// Materials API
export const materialsAPI = {
  getAll: (params) => api.get('/materials', { params }),
  getById: (id) => api.get(`/materials/${id}`),
  create: (data) => api.post('/materials', data),
  update: (id, data) => api.put(`/materials/${id}`, data),
  delete: (id) => api.delete(`/materials/${id}`),
  getCategories: () => api.get('/categories'),
};

// Admin API
export const adminAPI = {
  getDashboard: () => api.get('/admin/dashboard'),
  getUsers: (params) => api.get('/admin/users', { params }),
  updateUserStatus: (userId, isActive) => api.put(`/admin/users/${userId}/status`, { is_active: isActive }),
  getPendingVendors: () => api.get('/admin/vendors/pending'),
  verifyVendor: (vendorId, status, reason) => api.put(`/admin/vendors/${vendorId}/verify`, { status, rejection_reason: reason }),
  getPendingWorkers: () => api.get('/admin/workers/pending'),
  verifyWorker: (workerId, status, reason) => api.put(`/admin/workers/${workerId}/verify`, { status, rejection_reason: reason }),
};

// Workers API
export const workersAPI = {
  getAll: (params) => api.get('/workers', { params }),
  getById: (id) => api.get(`/workers/${id}`),
  getDashboardStats: () => api.get('/workers/dashboard/stats'),
  updateProfile: (data) => api.put('/workers/profile', data),
  getBookings: (params) => api.get('/bookings', { params }),
  updateBookingStatus: (bookingId, status) => api.put(`/bookings/${bookingId}/status`, { status }),
};

// Orders API
export const ordersAPI = {
  getAll: (params) => api.get('/orders', { params }),
  getById: (id) => api.get(`/orders/${id}`),
  updateStatus: (id, status) => api.put(`/orders/${id}/status`, { status }),
  getAnalytics: (params) => api.get('/orders/analytics', { params }),
};

// Payments API
export const paymentsAPI = {
  getTransactions: (params) => api.get('/payments/transactions', { params }),
  getAnalytics: (params) => api.get('/payments/analytics', { params }),
  processRefund: (paymentId, amount, reason) => api.post('/payments/refund', { payment_id: paymentId, amount, reason }),
};

// Utility functions
export const setAuthToken = (token) => {
  localStorage.setItem('authToken', token);
};

export const getAuthToken = () => {
  return localStorage.getItem('authToken');
};

export const removeAuthToken = () => {
  localStorage.removeItem('authToken');
  localStorage.removeItem('userData');
};

export const setUserData = (userData) => {
  localStorage.setItem('userData', JSON.stringify(userData));
};

export const getUserData = () => {
  const userData = localStorage.getItem('userData');
  return userData ? JSON.parse(userData) : null;
};

// File upload utility
export const uploadFile = async (file, type = 'image') => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);
  
  try {
    const response = await api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data.url;
  } catch (error) {
    throw new Error('File upload failed');
  }
};

// Export default api instance
export default api;
