import { useContext } from 'react';

// This would normally be imported from a context file
// For now, we'll create a simple hook that works with localStorage

export const useAuth = () => {
  const user = JSON.parse(localStorage.getItem('userData') || 'null');
  const token = localStorage.getItem('authToken');

  const login = (userData, authToken) => {
    localStorage.setItem('userData', JSON.stringify(userData));
    localStorage.setItem('authToken', authToken);
    window.location.reload(); // Simple reload for demo
  };

  const logout = () => {
    localStorage.removeItem('userData');
    localStorage.removeItem('authToken');
    window.location.href = '/login';
  };

  return {
    user,
    token,
    loading: false,
    login,
    logout,
    isAuthenticated: !!user && !!token,
  };
};
